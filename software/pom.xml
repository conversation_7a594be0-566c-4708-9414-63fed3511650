<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.jaguarlandrover.d9.oneappbackend</groupId>
    <artifactId>oab-api-gateway-software</artifactId>
    <version>1.0</version>
    <packaging>pom</packaging>
    <name>Lambda functions for OneApp API Gateway</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.target>17</maven.compiler.target>
        <maven.compiler.source>17</maven.compiler.source>

        <!-- Versions for Dependencies -->
        <awssdk-bom.version>2.21.46</awssdk-bom.version>
        <aws-lambda-java-core.version>1.2.3</aws-lambda-java-core.version>
        <aws-lambda-java-events.version>3.11.1</aws-lambda-java-events.version>
        <aws-lambda-java-log4j2.version>1.6.0</aws-lambda-java-log4j2.version>
        <byte-buddy.version>1.12.22</byte-buddy.version>
        <crac.version>0.1.3</crac.version>
        <datadog-lambda-java.version>1.4.10</datadog-lambda-java.version>
        <jackson.version>2.15.0-rc1</jackson.version>
        <jjwt.version>0.11.5</jjwt.version>
        <junit.version>5.10.2</junit.version>
        <junit-pioneer.version>2.2.0</junit-pioneer.version>
        <log4j.version>2.23.1</log4j.version>
        <logback.version>1.5.6</logback.version>
        <lombok.version>1.18.32</lombok.version>
        <objenesis.version>3.3</objenesis.version>
        <opentracing.version>0.33.0</opentracing.version>
        <powertools-cloudformation.version>1.15.0</powertools-cloudformation.version>
        <spock.version>2.4-M4-groovy-4.0</spock.version>

        <!-- Version for Vulnerabilities Fixes -->
        <jnr-posix.version>3.1.16</jnr-posix.version>

        <!-- Versions for Plugins -->
        <gmavenplus-plugin.version>2.1.0</gmavenplus-plugin.version>
        <jacoco-maven-plugin.version>0.8.12</jacoco-maven-plugin.version>
        <log4j-maven-shade-plugin-extensions.version>2.20.0</log4j-maven-shade-plugin-extensions.version>
        <maven-checkstyle-plugin.version>3.4.0</maven-checkstyle-plugin.version>
        <maven-shade-plugin.version>3.6.0</maven-shade-plugin.version>
        <maven-surefire-plugin.version>3.3.0</maven-surefire-plugin.version>
        <pitest-junit5-plugin.version>1.2.1</pitest-junit5-plugin.version>
        <pitest-maven-plugin.version>1.16.1</pitest-maven-plugin.version>

        <!-- Temporary exclusion until MOB-10429 -->
        <sonar.exclusions>
            **/com/jaguarlandrover/d9/oneappbackend/lambda/OabTokenLambdaAuthorizer.java,
            **/com/jaguarlandrover/d9/oneappbackend/lambda/EniIpLookup.java,
            **/com/jaguarlandrover/d9/oneappbackend/lambda/client/CachedAwsClient.java,
        </sonar.exclusions>

    </properties>

    <!-- List each lambda function project here. -->
    <modules>
        <module>library</module>
        <module>oabtokenauthorizer</module>
        <module>eniiplookup</module>
        <module>commandsproxyhandler</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>${awssdk-bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- AWS -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>apache-client</artifactId>
                <version>${awssdk-bom.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ec2</artifactId>
                <version>${awssdk-bom.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.lambda</groupId>
                <artifactId>powertools-cloudformation</artifactId>
                <version>${powertools-cloudformation.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.crac</groupId>
                <artifactId>org-crac</artifactId>
                <version>${crac.version}</version>
            </dependency>

            <!-- AWS Lambda -->
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-lambda-java-core</artifactId>
                <version>${aws-lambda-java-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-lambda-java-events</artifactId>
                <version>${aws-lambda-java-events.version}</version>
            </dependency>

            <!-- AWS Lambda Logging-->
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-lambda-java-log4j2</artifactId>
                <version>${aws-lambda-java-log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-slf4j2-impl</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-to-slf4j</artifactId>
                <version>${log4j.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>

            <!-- Logging in JSON format -->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-layout-template-json</artifactId>
                <version>${log4j.version}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jjwt.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jjwt.version}</version>
                <scope>runtime</scope>
            </dependency>

            <!-- Datadog Tracing -->
            <dependency>
                <groupId>com.datadoghq</groupId>
                <artifactId>datadog-lambda-java</artifactId>
                <version>${datadog-lambda-java.version}</version>
                <scope>compile</scope>
                <exclusions>
                    <exclusion>
                        <groupId>com.github.jnr</groupId>
                        <artifactId>jnr-posix</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.squareup.okio</groupId>
                        <artifactId>okio</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- Fix snyk vulnerabilities -->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-handler</artifactId>
                <version>4.1.118.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-common</artifactId>
                <version>4.1.118.Final</version>
            </dependency>

            <!-- Utilities -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- Testing -->
            <dependency>
                <groupId>org.junit.jupiter</groupId>
                <artifactId>junit-jupiter-engine</artifactId>
                <version>${junit.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.spockframework</groupId>
                <artifactId>spock-core</artifactId>
                <version>${spock.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>net.bytebuddy</groupId>
                <artifactId>byte-buddy</artifactId>
                <version>${byte-buddy.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.junit-pioneer</groupId>
                <artifactId>junit-pioneer</artifactId>
                <version>${junit-pioneer.version}</version>
                <scope>test</scope>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.objenesis/objenesis -->
            <dependency>
                <groupId>org.objenesis</groupId>
                <artifactId>objenesis</artifactId>
                <version>${objenesis.version}</version>
                <scope>test</scope>
            </dependency>

            <!-- Added for MOB-9754 -->
            <dependency>
                <groupId>net.spy</groupId>
                <artifactId>spymemcached</artifactId>
                <version>2.12.3</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.pitest</groupId>
                    <artifactId>pitest-maven</artifactId>
                    <version>${pitest-maven-plugin.version}</version>
                    <dependencies>
                        <dependency>
                            <groupId>org.pitest</groupId>
                            <artifactId>pitest-junit5-plugin</artifactId>
                            <version>${pitest-junit5-plugin.version}</version>
                        </dependency>
                    </dependencies>
                    <configuration>
                        <excludedClasses>
                            <param>com.jaguarlandrover.d9.oneappbackend.lambda.client.*</param>
                            <param>com.jaguarlandrover.d9.oneappbackend.lambda.dto.*</param>
                            <param>com.jaguarlandrover.d9.oneappbackend.lambda.factory.*</param>
                            <param>com.jaguarlandrover.d9.oneappbackend.lambda.helper.*</param>
                            <param>com.jaguarlandrover.d9.oneappbackend.lambda.util.*</param>
                        </excludedClasses>
<!--                    Integration tests should be excluded in mutation tests-->
                        <excludedTestClasses>
                            <param>com.jaguarlandrover.d9.oneappbackend.lambda.*SpecIT</param>
                            <param>com.jaguarlandrover.d9.oneappbackend.lambda.**.*SpecIT</param>
                        </excludedTestClasses>
                        <mutationThreshold>100</mutationThreshold>
                        <coverageThreshold>80</coverageThreshold>
                    </configuration>
                    <executions>
                        <execution>
                            <id>pitest-mutation-coverage</id>
                            <phase>verify</phase>
                            <goals>
                                <goal>mutationCoverage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>${maven-shade-plugin.version}</version>
                <dependencies>
                    <dependency>
                        <groupId>io.github.edwgiz</groupId>
                        <artifactId>log4j-maven-shade-plugin-extensions</artifactId>
                        <version>${log4j-maven-shade-plugin-extensions.version}</version>
                    </dependency>
                </dependencies>
                <configuration>
                    <createDependencyReducedPom>false</createDependencyReducedPom>
                    <transformers>
                        <transformer
                                implementation="io.github.edwgiz.log4j.maven.plugins.shade.transformer.Log4j2PluginCacheFileTransformer">
                        </transformer>
                    </transformers>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Detect tests -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <includes>
                        <include>**/*Spec</include>
                    </includes>
                    <failIfNoTests>true</failIfNoTests>
                    <useFile>false</useFile>
                </configuration>
            </plugin>

            <!-- Spock tests -->
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>${gmavenplus-plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>addTestSources</goal>
                            <goal>compile</goal>
                            <goal>compileTests</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Check source code formatting -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${maven-checkstyle-plugin.version}</version>
                <configuration>
                    <configLocation>../config/checkstyle.xml</configLocation>
                    <consoleOutput>true</consoleOutput>
                    <violationSeverity>warning</violationSeverity>
                    <failsOnError>true</failsOnError>
                    <excludes>
                        com/jaguarlandrover/d9/oneappbackend/lambda/**/Statement.java,com/jaguarlandrover/d9/oneappbackend/lambda/**/PolicyDocument.java
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Compute and report code coverage -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>