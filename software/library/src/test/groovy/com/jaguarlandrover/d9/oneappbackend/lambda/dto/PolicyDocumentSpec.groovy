/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.dto

import com.fasterxml.jackson.databind.ObjectMapper
import com.jaguarlandrover.d9.oneappbackend.lambda.library.dto.PolicyDocument
import com.jaguarlandrover.d9.oneappbackend.lambda.library.dto.Statement
import spock.lang.Specification

import static org.junit.jupiter.api.Assertions.assertFalse
import static org.junit.jupiter.api.Assertions.assertTrue


class PolicyDocumentSpec extends Specification {

    public static final String VERSION = "2012-10-17"
    public static final String ACTION = "execute-api:Invoke"

    ObjectMapper objectMapper = new ObjectMapper()

    def "serialization works as expected"() {
        given: ""
        def policyDocument = PolicyDocument.builder()
                .version(VERSION)
                .statements(List.of(createStatement(effect, resource)))
                .build()

        when:
        def resultText = objectMapper.writeValueAsString(policyDocument)

        then:
        //assertEquals(expectedText, resultText)
        assertTrue(resultText.contains("Version"))
        assertTrue(resultText.contains("Statement"))

        assertFalse(resultText.contains("version"))
        assertFalse(resultText.contains("statement"))
        assertFalse(resultText.contains("statements"))

        where:
        effect  | resource          || expectedText
        "Allow" | "aws-resource-1"  || ""
        "Deny"  | "aws-resource-2"  || ""
    }

    Statement createStatement(String effect, String resource) {

        return Statement.builder()
                .action(ACTION)
                .effect(effect)
                .resource(resource)
                .build()
    }


}