/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.dto

import com.fasterxml.jackson.databind.ObjectMapper
import com.jaguarlandrover.d9.oneappbackend.lambda.library.dto.Statement
import spock.lang.Specification

import static org.junit.jupiter.api.Assertions.assertFalse
import static org.junit.jupiter.api.Assertions.assertTrue


class StatementSpec extends Specification {

    public static final String ACTION = "execute-api:Invoke"

    ObjectMapper objectMapper = new ObjectMapper()

    def "serialization works as expected"() {
        given: ""
        def statement = Statement.builder()
                .action(ACTION)
                .effect(effect)
                .resource(resource)
                .build()

        when:
        def resultText = objectMapper.writeValueAsString(statement)

        then:
        // Properties start with capital letter
        assertTrue(resultText.contains("Action"))
        assertTrue(resultText.contains("Effect"))
        assertTrue(resultText.contains("Resource"))

        assertFalse(resultText.contains("action"))
        assertFalse(resultText.contains("effect"))
        assertFalse(resultText.contains("resource"))

        where:
        effect  | resource          || expectedText
        "Allow" | "aws-res-1"       || ""
        "Deny"  | "aws-res-2"       || ""
    }

    Statement createStatement(String effect, String resource) {
        return Statement.builder()
                .action(ACTION)
                .effect(effect)
                .resource(resource)
                .build()
    }


}