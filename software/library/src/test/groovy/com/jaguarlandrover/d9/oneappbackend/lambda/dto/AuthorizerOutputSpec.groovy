/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.dto

import com.fasterxml.jackson.databind.ObjectMapper
import com.jaguarlandrover.d9.oneappbackend.lambda.library.dto.AuthorizerOutput
import com.jaguarlandrover.d9.oneappbackend.lambda.library.dto.PolicyDocument
import com.jaguarlandrover.d9.oneappbackend.lambda.library.dto.Statement
import spock.lang.Specification

import static org.junit.jupiter.api.Assertions.assertFalse
import static org.junit.jupiter.api.Assertions.assertTrue

class AuthorizerOutputSpec extends Specification {

    public static final String PRINCIPAL_ID = "some-Id"
    public static final String VERSION = "2012-10-17"
    public static final String ACTION = "execute-api:Invoke"

    ObjectMapper objectMapper = new ObjectMapper()

    def "serialization works as expected"() {
        given: "policy statement"
        def policyDocument = PolicyDocument.builder()
                .version(VERSION)
                .statements(List.of(createStatement(effect, resource)))
                .build()

        and: "authorizer output"
        def authorizerOutput = AuthorizerOutput.builder()
                .principalId(PRINCIPAL_ID)
                .policyDocument(policyDocument)
                .context(Map.of("key1", "value1"))
                .build()

        when:
        def resultText = objectMapper.writeValueAsString(authorizerOutput)

        then:
        assertTrue(resultText.contains("principalId"))
        assertTrue(resultText.contains("Version"))
        assertTrue(resultText.contains("Statement"))

        assertFalse(resultText.contains("version"))
        assertFalse(resultText.contains("statement"))
        assertFalse(resultText.contains("statements"))

        where:
        effect  | resource
        "Allow" | "aws-resource-1"
        "Deny"  | "aws-resource-2"
    }

    def "authorizer output contains principalId and context"() {
        given: "policy statement"
        def policyDocument = PolicyDocument.builder()
                .version(VERSION)
                .statements(List.of(createStatement("Allow","aws-resource")))
                .build()

        and: "authorizer output"
        def authorizerOutput = AuthorizerOutput.builder()
                .principalId(PRINCIPAL_ID)
                .policyDocument(policyDocument)
                .context(Map.of("key1", "value1"))
                .build()

        when:
        def principalId = authorizerOutput.getPrincipalId()
        def context = authorizerOutput.getContext()

        then:
        assertFalse(principalId.isEmpty())
        assertFalse(context.isEmpty())
    }

    Statement createStatement(String effect, String resource) {
        return Statement.builder()
                .action(ACTION)
                .effect(effect)
                .resource(resource)
                .build()
    }

}