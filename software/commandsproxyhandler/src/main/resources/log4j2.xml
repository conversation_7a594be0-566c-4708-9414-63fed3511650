<?xml version="1.0" encoding="UTF-8"?>
<Configuration packages="com.amazonaws.services.lambda.runtime.log4j2" status="trace">
    <Appenders>
        <Lambda name="Lambda" format="JSON">
            <LambdaTextFormat>
                <PatternLayout>
                    <pattern>%d{yyyy-MM-dd HH:mm:ss} %X{AWSRequestId} %-5p %c{1}:%L - %m%n</pattern>
                </PatternLayout>
            </LambdaTextFormat>
            <LambdaJSONFormat>
                <JsonTemplateLayout eventTemplateUri="classpath:EcsLayout.json" />
            </LambdaJSONFormat>
        </Lambda>
    </Appenders>
    <Loggers>
        <Root level="INFO">
            <AppenderRef ref="Lambda" />
        </Root>
        <Logger name="software.amazon.awssdk" level="WARN" />
        <Logger name="software.amazon.awssdk.request" level="DEBUG" />
    </Loggers>
</Configuration>