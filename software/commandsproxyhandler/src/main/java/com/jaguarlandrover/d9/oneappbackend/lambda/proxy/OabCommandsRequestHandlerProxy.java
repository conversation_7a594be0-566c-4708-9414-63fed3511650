/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.proxy;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent;
import com.jaguarlandrover.d9.oneappbackend.lambda.proxy.model.CommandControlResponse;
import com.jaguarlandrover.d9.oneappbackend.lambda.proxy.util.CommandType;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Instant;
import java.util.Map;
import java.util.Objects;
import lombok.SneakyThrows;
import org.apache.http.HttpHeaders;
import org.crac.Core;
import org.crac.Resource;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.utils.StringUtils;


/**
 * A handler function for forwarding a request from an API gateway to the command router.
 * It parses out the information from the URL (vehicleID & command type) to construct the url for the command router.
 * The request body is expected to be the same as the command router so is un-touched in this function.
 */
public class OabCommandsRequestHandlerProxy implements Resource, RequestHandler<APIGatewayProxyRequestEvent, APIGatewayProxyResponseEvent> {

  protected static final Logger log = LoggerFactory.getLogger(OabCommandsRequestHandlerProxy.class);

  private static final String ENV = "env";
  private static final String ENVIRONMENT = "environment";
  private static final Map<String, Object> tags =
      Map.of(ENV, Objects.requireNonNullElse(System.getenv(ENVIRONMENT), "local"));
  private static final String COMMAND_ROUTER_HOST_VARIABLE = "commandRouterHost";
  private static final String HOP_TIMESTAMP = "Hop-Timestamp";
  private static final String ONE_APP_OAB_ROUTER_CALL = "One-App-Oab-Router-Call";
  public static final String CORRELATION_ID_HEADER = "Correlation-Id";
  public static final String PROCESSING_TIME = "Processing-time";

  private final HttpClient httpClient;
  private final String commandRouterUrl;


  /**
   * Constructor.
   */
  public OabCommandsRequestHandlerProxy() {
    this(HttpClient.newBuilder().build(), System.getenv(COMMAND_ROUTER_HOST_VARIABLE));
    Core.getGlobalContext().register(this);
  }

  public OabCommandsRequestHandlerProxy(HttpClient httpClient, String commandRouterUrl) {
    this.httpClient = httpClient;
    this.commandRouterUrl = commandRouterUrl;
  }

  @SneakyThrows
  @Override
  public APIGatewayProxyResponseEvent handleRequest(final APIGatewayProxyRequestEvent input, final Context context) {
    final long processingTimeStart = Instant.now().toEpochMilli();

    CommandType commandType = CommandType.getLabel(input.getPath());
    log.info("handleRequest({}) called. START_OabLambdaProcess: {} CommandType: {}", input, processingTimeStart, commandType);

    String appHopTimeStamp = input.getHeaders().get(HOP_TIMESTAMP);
    //set to processingTimeStart or use value from header passed down from app so the latency can be calculated
    long appToOabLatencyTimestamp = processingTimeStart;
    if (!StringUtils.isEmpty(appHopTimeStamp)) {
      appToOabLatencyTimestamp = Long.parseLong(appHopTimeStamp);
    }
    long appToOabHoptime = processingTimeStart - appToOabLatencyTimestamp;

    //Get params needed for request
    final String authToken = input.getHeaders().get(HttpHeaders.AUTHORIZATION);
    String vehicleId = input.getPathParameters().get("vehicleId");

    String requestId = (context != null && context.getAwsRequestId() != null)
                        ? context.getAwsRequestId()
                        : "nullRequestId";

    log.info("x_amzn_RequestId Request ID: {}", requestId);

    APIGatewayProxyResponseEvent response = new APIGatewayProxyResponseEvent();

    // make HTTP request to Command router!
    CommandControlResponse httpResponse = sendRequest(input, vehicleId, authToken, requestId, appToOabHoptime, processingTimeStart);

    log.info("Command and control response: {}", httpResponse.body());
    response.setBody(httpResponse.body());
    response.setStatusCode(httpResponse.statusCode());
    response.setHeaders(Map.of(
        CORRELATION_ID_HEADER, requestId,
        PROCESSING_TIME, Instant.now().toEpochMilli() - processingTimeStart + " ms"
    ));

    log.info("STOP_OabLambdaProcess: {} CommandType: {}", Instant.now().toEpochMilli() - processingTimeStart, commandType);

    return response;
  }

  private CommandControlResponse sendRequest(APIGatewayProxyRequestEvent request, String vehicleId,
                                             String authToken, String requestId, long appToOabHoptime,
                                             long commandRouterProcessingTimeStart)
                                              throws IOException, InterruptedException {
    log.info("sendRequest({}, {}, {}) called.", commandRouterUrl, vehicleId, request);

    // create proxy path to append path params to command router endpoint
    String path = request.getPath();
    String proxyPath = path.split("/commands")[1];

    String url = commandRouterUrl + "/vehicle/" + vehicleId + proxyPath;

    log.info("URL: {}", url);

    HttpRequest.BodyPublisher bodyPublisher;
    JSONObject body = new JSONObject(request.getBody());

    bodyPublisher = HttpRequest.BodyPublishers.ofString(String.valueOf(body));
    HttpRequest httpRequest = null;
    try {

      httpRequest = HttpRequest.newBuilder()
              .uri(new URI(url))
              .headers(HttpHeaders.CONTENT_TYPE, "application/json", HttpHeaders.AUTHORIZATION, authToken,
                CORRELATION_ID_HEADER, requestId,
                HOP_TIMESTAMP, String.valueOf(Instant.now().toEpochMilli()),
                ONE_APP_OAB_ROUTER_CALL,
                String.valueOf(appToOabHoptime + (Instant.now().toEpochMilli() - commandRouterProcessingTimeStart)))
              .POST(bodyPublisher)
              .build();
    } catch (URISyntaxException e) {
      log.info("wrong url syntax");
    }

    HttpResponse<String> response = httpClient.send(httpRequest, HttpResponse.BodyHandlers.ofString());

    return new CommandControlResponse(response.statusCode(), response.body());
  }

  /**
   * Run after a restore.
   */
  @Override
  public void afterRestore(org.crac.Context<? extends Resource> context) {
    // not used.
  }

  /**
   * Run before a snapshot.
   */
  @Override
  public void beforeCheckpoint(org.crac.Context<? extends Resource> context) {
    log.info("beforeCheckpoint() called.");
    try {
      APIGatewayProxyRequestEvent event = new APIGatewayProxyRequestEvent();
      event.withHeaders(Map.of(HttpHeaders.AUTHORIZATION, "fake-token-value", CORRELATION_ID_HEADER, "fakeRequestId"));
      event.withPathParameters(Map.of("vehicleId", "f97d81b3-a95f-4f93-bfb7-70efd33248fe"));
      event.withPath("/vehicles/f97d81b3-a95f-4f93-bfb7-70efd33248fe/commands/door");
      event.withBody("""
          {
            "option": "LOCK"
          }
          """);
      handleRequest(event, null);
    } catch (Exception ex) {
      log.error("Failed to prime function with error: {}", ex.getMessage());
    }

  }
}
