package com.jaguarlandrover.d9.oneappbackend.lambda.proxy.util;

import java.util.Map;

public enum CommandType {

  BEEP_FLASH("beepflash"), CAC("cabinairclean"), DOOR("door"), ALARM_RESET("alarmreset"), UNKNOWN("unknown");

  private final String label;

  CommandType(String label) {
    this.label = label;
  }

  @Override
  public String toString() {
    return label;
  }

  /**
   * This method accepts the path from the APIGatewayProxyRequestEvent and returns the metric
   * label to be used as part of the metric name sent to Datadog.
   */
  public static CommandType getLabel(String path) {
    String[] splitPath = path.split("/commands/");
    if (splitPath.length < 2) {
      return CommandType.UNKNOWN;
    }
    CommandType label = commandLabelMap.get(splitPath[1]);
    if (label == null) {
      return CommandType.UNKNOWN;
    }
    return label;
  }

  private static final Map<String, CommandType> commandLabelMap =
      Map.of(BEEP_FLASH.label, BEEP_FLASH, CAC.label, CAC, DOOR.label, DOOR, ALARM_RESET.label, ALARM_RESET);
}

