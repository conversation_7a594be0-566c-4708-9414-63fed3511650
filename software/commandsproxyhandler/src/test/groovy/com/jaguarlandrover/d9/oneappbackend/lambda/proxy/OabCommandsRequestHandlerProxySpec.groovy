/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.proxy

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyResponseEvent
import org.crac.Core
import org.json.JSONObject
import org.junitpioneer.jupiter.SetEnvironmentVariable
import software.amazon.awssdk.http.HttpStatusCode
import spock.lang.Specification
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse

import static org.junit.jupiter.api.Assertions.assertEquals

@SetEnvironmentVariable(key = "commandRouterHost", value = "https://rvc.dev.jlr-vcdp.com")
class OabCommandsRequestHandlerProxySpec extends Specification {

    HttpClient mockClient = Mock()
    OabCommandsRequestHandlerProxy function = new OabCommandsRequestHandlerProxy(mockClient, "https://rvc.dev.jlr-vcdp.com")

    def "test no exception thrown when calling the lambda constructor"() {

        when:
        new OabCommandsRequestHandlerProxy()

        then:
        noExceptionThrown()

    }

    def "test command door lock return 200"() {

        given: "a valid vehicle ID is used"
        String vehicleId = "8537be4f-7fba-4a19-a691-04e8020c7b82"

        and: "command router returns a response as expected"
        JSONObject responseFromCommandRouter = new JSONObject()
        responseFromCommandRouter.put("option", "LOCK")
        responseFromCommandRouter.put("vehicleId", vehicleId)
        responseFromCommandRouter.put("created", "2024-11-05T08:15:30-05:00")

        HttpResponse httpResponse = Mock()
        httpResponse.statusCode() >> HttpStatusCode.OK
        httpResponse.body() >> responseFromCommandRouter.toString()
        mockClient.send(_, _) >> httpResponse

        and: "a valid request object is used"
        JSONObject object = new JSONObject()
        object.put("option", "LOCK")

        when: "the API is called"
        String inboundPath = String.format("/vehicles/%s/commands/door", vehicleId)
        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent()
                .withBody(object.toString())
                .withHeaders(getHeaders())
                .withPathParameters(getPathParameters())
                .withPath(inboundPath)
        APIGatewayProxyResponseEvent result = function.handleRequest(request, null)

        then: "response code should be 200"
        assertEquals(200, result.getStatusCode())
        assert result.getStatusCode() == 200
        responseFromCommandRouter.toString() == result.getBody()
        assertEquals("testRequestId", request.getHeaders().get("rvc-requestId"))

    }

    def "test command door lock return 200 with extended path"() {

        given: "a valid vehicle ID is used"
        String vehicleId = "8537be4f-7fba-4a19-a691-04e8020c7b82"

        and: "command router returns a response as expected"
        def captured
        JSONObject responseFromCommandRouter = new JSONObject()
        responseFromCommandRouter.put("option", "LOCK")
        responseFromCommandRouter.put("vehicleId", vehicleId)
        responseFromCommandRouter.put("created", "2024-11-05T08:15:30-05:00")

        HttpResponse httpResponse = Mock()
        httpResponse.statusCode() >> HttpStatusCode.OK
        httpResponse.body() >> responseFromCommandRouter.toString()

        mockClient.send(_, _) >> { arguments -> captured = arguments; return httpResponse }

        and: "a valid request object is used"
        JSONObject object = new JSONObject()
        object.put("option", "LOCK")

        when: "the API is called"
        String inboundPath = String.format("/vehicles/%s/commands/door/extended/path/test", vehicleId)
        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent()
                .withBody(object.toString())
                .withHeaders(getHeaders())
                .withPathParameters(getPathParameters())
                .withPath(inboundPath)
        APIGatewayProxyResponseEvent result = function.handleRequest(request, null)

        then: "response code should be 200"
        assertEquals(200, result.getStatusCode())

        and: "uri used contains all path parameters"
        HttpRequest actualHttpRequest = (HttpRequest) captured[0]
        actualHttpRequest.uri().toString() == "https://rvc.dev.jlr-vcdp.com/vehicle/vehicleId12345678/door/extended/path/test"
        assertEquals("testRequestId", request.getHeaders().get("rvc-requestId"))

    }

    def "test command door lock return 401 when Command Router returns 401"() {

        given: "a valid vehicle ID is used"
        String vehicleId = "8537be4f-7fba-4a19-a691-04e8020c7b82"

        and: "command router returns a 401 response"
        JSONObject responseFromCommandRouter = new JSONObject()

        HttpResponse httpResponse = Mock()
        httpResponse.statusCode() >> HttpStatusCode.UNAUTHORIZED
        httpResponse.body() >> responseFromCommandRouter.toString()
        mockClient.send(_, _) >> httpResponse

        and: "a valid request object is used"
        JSONObject object = new JSONObject()
        object.put("option", "LOCK")

        when: "the API is called"
        String inboundPath = String.format("/vehicles/%s/commands/door", vehicleId)
        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent()
                .withBody(object.toString())
                .withHeaders(getHeaders())
                .withPathParameters(getPathParameters())
                .withPath(inboundPath)
        APIGatewayProxyResponseEvent result = function.handleRequest(request, null)

        then: "response code should be 500"
        assertEquals(401, result.getStatusCode())
        assertEquals("testRequestId", request.getHeaders().get("rvc-requestId"))

    }

    def "test no exception thrown when calling beforeCheckpoint of CRaC"(){
        given:
        def context = Core.getGlobalContext()

        when:
        function.beforeCheckpoint(context)

        then:
        noExceptionThrown()
    }

    private Map<String, String> getHeaders(){
        return Map.of("Authorization", "forgeRockToken",
                "rvc-requestId", "testRequestId")
    }

    private Map<String, String> getPathParameters(){
        return Map.of("vehicleId", "vehicleId12345678",
                "command","door")

    }

}
