package com.jaguarlandrover.d9.oneappbackend.lambda.proxy

import com.jaguarlandrover.d9.oneappbackend.lambda.proxy.util.CommandType
import spock.lang.Specification

import static org.junit.jupiter.api.Assertions.assertEquals

class CommandTypeSpec extends Specification {

    def "test MetricLabel returns correct label with valid path and metrics are reported for that command"() {
        given: "a valid path for command where metrics are reported"
        String path = "/api/v1/vehicles/845d0762-b243-4cd6-94df-3feb3a68e9df/commands/beepflash"

        when: "getLabel is called"
        String metricLabel = CommandType.getLabel(path)

        then: "method should return evcharge"
        assertEquals("beepflash", metricLabel)
    }

    def "test MetricLabel returns unknown with valid path but metrics not reported for that command"() {
        given: "a valid path for command where metrics are not reported"
        String path = "/api/v1/vehicles/845d0762-b243-4cd6-94df-3feb3a68e9df/commands/evcharge"

        when: "method is called"
        String metricLabel = CommandType.getLabel(path)

        then: "method should return unknown"
        assertEquals("unknown", metricLabel)
    }

    def "test MetricLabel returns unknown with invalid path"() {
        given: "a invalid path"
        String path = "/api/v1/vehicles/evcharge"

        when: "method is called"
        String metricLabel = CommandType.getLabel(path)

        then: "method should return unknown"
        assertEquals("unknown", metricLabel)
    }

}
