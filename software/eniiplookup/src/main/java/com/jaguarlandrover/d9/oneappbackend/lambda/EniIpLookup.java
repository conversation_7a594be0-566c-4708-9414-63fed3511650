/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.events.CloudFormationCustomResourceEvent;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.services.ec2.Ec2Client;
import software.amazon.awssdk.services.ec2.model.DescribeNetworkInterfacesRequest;
import software.amazon.awssdk.services.ec2.model.NetworkInterface;
import software.amazon.lambda.powertools.cloudformation.AbstractCustomResourceHandler;
import software.amazon.lambda.powertools.cloudformation.Response;

public class EniIpLookup extends AbstractCustomResourceHandler {
  private static final Logger LOGGER = LoggerFactory.getLogger(EniIpLookup.class);

  private static final String PHYSICAL_ID = "ENIPrivateIPResource";

  private final Ec2Client ec2Client = Ec2Client.builder().build();

  @Override
  protected Response create(CloudFormationCustomResourceEvent cloudFormationCustomResourceEvent,
                            Context context) {
    try {
      LOGGER.info("[EniIpLookup] Executing creation / update of custom resource");
      if (!(cloudFormationCustomResourceEvent.getResourceProperties()
          .get("vpce_enis") instanceof List)) {
        throw new RuntimeException("vpce_enis property is not a list");
      }
      var eniIds = (List<String>)
          cloudFormationCustomResourceEvent.getResourceProperties().get("vpce_enis");
      LOGGER.info("[EniIpLookup] {}", String.format("ENI IDs: %s", eniIds));
      var enis = ec2Client.describeNetworkInterfaces(DescribeNetworkInterfacesRequest.builder()
          .networkInterfaceIds(eniIds).build());
      var ips = enis.networkInterfaces().stream().map(NetworkInterface::privateIpAddress)
          .collect(Collectors.toList());
      var returnData = new HashMap<String, String>();
      var idx = 0;
      for (var ip : ips) {
        returnData.put(String.format("IP%d", idx), ip);
        idx = idx + 1;
      }
      returnData.put("IPS", ips.toString());
      LOGGER.info("[EniIpLookup] {}", String.format("ENI IPs: %s", ips));

      return Response.builder()
          .physicalResourceId(PHYSICAL_ID)
          .status(Response.Status.SUCCESS)
          .value(returnData)
          .build();
    } catch (Exception e) {
      LOGGER.error("[EniIpLookup] Failed fetching network interfaces", e);
      return Response.failed(PHYSICAL_ID);
    }
  }

  @Override
  protected Response update(CloudFormationCustomResourceEvent cloudFormationCustomResourceEvent,
                            Context context) {
    return create(cloudFormationCustomResourceEvent, context);
  }

  @Override
  protected Response delete(CloudFormationCustomResourceEvent cloudFormationCustomResourceEvent,
                            Context context) {
    return Response.builder()
        .status(Response.Status.SUCCESS)
        .build();
  }

}
