/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.client

import com.fasterxml.jackson.databind.ObjectMapper
import com.jaguarlandrover.d9.oneappbackend.lambda.dto.FrJwtKeyResponse
import com.jaguarlandrover.d9.oneappbackend.lambda.util.HttpRequestPreparer
import spock.lang.Specification

import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse

class FrApiClientSpec extends Specification {

    ObjectMapper mapper = new ObjectMapper()
    HttpRequestPreparer httpRequestPreparer = Mock()
    HttpClient httpClient = Mock()
    FrApiClient frApiClient = new FrApiClient(httpRequestPreparer, httpClient)

    def "fr api client checks validity of the token"() {

        given: "object mapper read mock response from /jwk_uri endpoint"
        def frJwtResponse = (!"".equalsIgnoreCase(keyFileName)) ? mapper.readValue(this.getClass().getResource(keyFileName).text, FrJwtKeyResponse) : ""

        and: "we have a mock request prepared for us"
        def mockRequest = Mock(HttpRequest)
        httpRequestPreparer.create() >> mockRequest

        and: "mock the response for the request"
        def mockResponse = Mock(HttpResponse)
        mockResponse.body() >> new ObjectMapper().writeValueAsString(frJwtResponse)
        httpClient.send(_, _) >> mockResponse

        when: "get validation method is called"
        def output = frApiClient.getFrTokenResponse()

        then: "response says whether token is active"
        output == mockResponse

        where:

        keyFileName                             || _
        "/response/fr-right-certificate.json"   || _
        "/response/fr-wrong-certificate.json"   || _
        "/response/fr-invalid-certificate.json" || _
    }

    def "responds with null when not connecting to the ForgeRock server"() {
        given:
        HttpRequestPreparer httpRequestPreparer = new HttpRequestPreparer("https://a.com")
        HttpClient httpClient = Mock()
        FrApiClient frApiClient = new FrApiClient(httpRequestPreparer, httpClient)

        when: "get validation method is called"
        def output = frApiClient.getFrTokenResponse()

        then: "exception thrown"
        output == null
    }
}
