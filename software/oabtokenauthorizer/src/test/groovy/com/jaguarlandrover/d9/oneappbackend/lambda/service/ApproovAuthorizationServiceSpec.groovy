/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.service

import com.jaguarlandrover.d9.oneappbackend.lambda.helper.ApproovTokenTestHelper
import spock.lang.Specification

class ApproovAuthorizationServiceSpec extends Specification {

    def "test approov token validation"(){

        given: "Approov token service"
        def authorizationService = new ApproovAuthorizationService()

        when: "validation method is called"
        def output = authorizationService.isApproovTokenValid(approovToken,
                ApproovTokenTestHelper.encodedApproovSecretKeyString)

        then: "check we get expected result"
        output == expectedResult

        where:
        approovToken                                      | expectedResult
        ApproovTokenTestHelper.buildApproovValidToken()   | true
        ApproovTokenTestHelper.buildApproovInvalidToken() | false
    }

}