/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda

import com.amazonaws.services.lambda.runtime.ClientContext
import com.amazonaws.services.lambda.runtime.CognitoIdentity
import com.amazonaws.services.lambda.runtime.Context
import com.amazonaws.services.lambda.runtime.LambdaLogger
import com.github.tomakehurst.wiremock.WireMockServer
import com.jaguarlandrover.d9.oneappbackend.lambda.client.OabMemcachedClient
import com.jaguarlandrover.d9.oneappbackend.lambda.helper.ApproovTokenTestHelper
import com.jaguarlandrover.d9.oneappbackend.lambda.helper.EnvironmentVariables
import com.jaguarlandrover.d9.oneappbackend.lambda.helper.SecretsManagerHelper
import net.spy.memcached.internal.GetFuture
import spock.lang.Shared
import spock.lang.Specification

import static com.github.tomakehurst.wiremock.client.WireMock.aResponse
import static com.github.tomakehurst.wiremock.client.WireMock.get
import static com.github.tomakehurst.wiremock.client.WireMock.urlPathEqualTo
import static com.github.tomakehurst.wiremock.core.WireMockConfiguration.wireMockConfig

class AbstractSpecificationIT extends Specification {

    def AUTH_HEADER = "Authorization"
    def ATTESTATION_HEADER = "Attestation"
    def CONTENT_TYPE_HEADER = "Content-Type"
    def VALID_USER_ID = "12345"
    static def CACHE_KEY = "X5C"

    static WireMockServer forgeRockServer

    @Shared
    EnvironmentVariables environmentVariables = Mock()
    @Shared
    OabMemcachedClient oabMemcachedClient = Mock()
    @Shared
    SecretsManagerHelper secretsManagerHelper = Mock()

    void 'prepare ForgeRock server for testing'() {
        forgeRockServer = new WireMockServer(wireMockConfig().port(8888))

        forgeRockServer.stubFor(get(urlPathEqualTo("/gateway/oauth2/realms/root/realms/customer/connect/jwk_uri"))
                .willReturn(aResponse()
                        .withHeader("Content-Type", "application/json")
                        .withStatus(200)
                        .withBody(OabTokenLambdaAuthorizerSpecIT.getResource("/response/fr-right-certificate.json").text)))
    }

    void 'mock environmental variables'() {
        environmentVariables.getVariable("FORGEROCK_HOST") >> "http://localhost:8888"
        environmentVariables.getVariable("CACHE_ENDPOINT") >> "localhost"
        environmentVariables.getVariable("CACHE_PORT") >> "9999"
        environmentVariables.getVariable("APPROOV_ENABLED") >> "true"
    }

    void 'mock memcached behaviour'() {
        def memcachedResponse = Mock(GetFuture)
        memcachedResponse.get(_, _) >> this.getClass().getResource("/response/cache-right-response.json").text
        oabMemcachedClient.asyncGet(CACHE_KEY) >> memcachedResponse
        oabMemcachedClient.initializeMemcachedClient(_, _) >> null
    }

    void 'mock secrets manager helper'() {
        secretsManagerHelper.getApproovSecretKey() >> ApproovTokenTestHelper.encodedApproovSecretKeyString
    }

    Context prepareContextForTest() {
        new Context() {
            @Override
            String getAwsRequestId() {
                return null
            }

            @Override
            String getLogGroupName() {
                return null
            }

            @Override
            String getLogStreamName() {
                return null
            }

            @Override
            String getFunctionName() {
                return "some-function-name"
            }

            @Override
            String getFunctionVersion() {
                return null
            }

            @Override
            String getInvokedFunctionArn() {
                return "arn:aws:lambda:eu-west-2:112233:function:some-function-name"
            }

            @Override
            CognitoIdentity getIdentity() {
                return null
            }

            @Override
            ClientContext getClientContext() {
                return null
            }

            @Override
            int getRemainingTimeInMillis() {
                return 0
            }

            @Override
            int getMemoryLimitInMB() {
                return 0
            }

            @Override
            LambdaLogger getLogger() {
                return null
            }
        }
    }
}