/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.helper

import io.jsonwebtoken.JwtBuilder
import java.text.SimpleDateFormat
import spock.lang.Specification

class TokenParserTestSpec extends Specification {

    final static String PRINCIPAL_ID_URN = "urn:iam2-mgd-v1:mgd-identifiers:oneapp-rangerover:auto-id:principal-uuid:%s"
    final static String AUTO_ID_URN = "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid:%s"
    final static String VEHICLE_ID_URN = "urn:iam2-mgd-v1:mgd-attributes:vehicle:vehicle-identity:iam2-consumer-rights-v1:digital-vehicle:vehicle-id:%s:%s"
    final static String SVCRM_ID_URN = "urn:iam2-mgd-v1:mgd-identifiers:customer:person:iam2-consumer-rights-v1:SVCRM:crmId:%s:%s"

    final static String JWT_MGD_ATTR_KEY = "mgd-attributes"
    final static String JWT_MGD_ID_KEY = "mgd-identifiers"

    static final String VALID_TOKEN_EXPIRATION_DATE = "3023-12-31"
    static final String INVALID_TOKEN_EXPIRATION_DATE = "2023-01-31"

    def "token parser extracts principal ID and vehicle IDs from token payload"() {

        given: "test token by user ID"
        // Test Token
        def subject = (userId != null)?String.format(PRINCIPAL_ID_URN, userId):null
        JwtBuilder validTokenJwtBuilder = new FrTokenTestHelper().buildBaseValidToken()
                .setSubject(subject)
                .setAudience(audience)
                .setExpiration(new SimpleDateFormat
                        ("yyyy-MM-dd").parse(expirationDate))

        validTokenJwtBuilder.claim(JWT_MGD_ATTR_KEY, mgd_attributes)

        String validToken = validTokenJwtBuilder.compact()

        when: "get validation method is called"
        def tokenParser = new TokenParser()
        def output = tokenParser.retrieveTokenAttributes(validToken)

        then: "response says whether token is active"
        output.get(TokenParser.PRINCIPAL_ID_KEY) == expectedPrincipalId
        output.get(TokenParser.VEHICLE_ID_KEY) == expectedVehicleIds

        where:
        expirationDate                  | userId  | audience                    | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | TokenParser.OAB_FR_AUDIENCE | _
        VALID_TOKEN_EXPIRATION_DATE     | "54321" | TokenParser.OAB_FR_AUDIENCE | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | TokenParser.OAB_FR_AUDIENCE | _
        VALID_TOKEN_EXPIRATION_DATE     | null    | TokenParser.OAB_FR_AUDIENCE | _

        VALID_TOKEN_EXPIRATION_DATE     | "12345" | "invalid-audience"          | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | "invalid-audience"          | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | null                        | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | null                        | _

        INVALID_TOKEN_EXPIRATION_DATE   | "12345" | TokenParser.OAB_FR_AUDIENCE | _
        __
        mgd_attributes                   || expectedPrincipalId || expectedVehicleIds
        exampleMgdAttributesSingleItem() || "12345"             || "12345678-1c23-8a45-d1d2-02ba1742890s"
        exampleMgdAttributesMultiples()  || "54321"             || "12345678-1c23-8a45-d1d2-02ba1742890s,87643210-2d45-9b56-a1a6-02ba1123137b"
        emptyListOfVehicleIds()          || "12345"             || ""
        exampleMgdAttributesMultiples()  || null                || null

        exampleMgdAttributesSingleItem() || null                || null
        emptyListOfVehicleIds()          || null                || null
        exampleMgdAttributesSingleItem() || null                || null
        emptyListOfVehicleIds()          || null                || null

        exampleMgdAttributesMultiples()  || null                || null
    }

    def exampleMgdAttributesSingleItem() {
        List.of(String.format(VEHICLE_ID_URN, "12345678-1c23-8a45-d1d2-02ba1742890s", "driver"))
    }

    def exampleMgdAttributesMultiples() {
        List.of(String.format(VEHICLE_ID_URN, "12345678-1c23-8a45-d1d2-02ba1742890s", "driver"),
                String.format(VEHICLE_ID_URN, "87643210-2d45-9b56-a1a6-02ba1123137b", "owner"))
    }

    def emptyListOfVehicleIds() {
        new ArrayList<>()
    }

    def "token parser extracts principal ID and Auto ID from token payload"() {

        given: "test token by user ID"
        // Test Token
        def subject = (userId != null)?String.format(PRINCIPAL_ID_URN, userId):null
        JwtBuilder validTokenJwtBuilder = new FrTokenTestHelper().buildBaseValidToken()
                .setSubject(subject)
                .setAudience(audience)
                .setExpiration(new SimpleDateFormat
                        ("yyyy-MM-dd").parse(expirationDate))

        validTokenJwtBuilder.claim(JWT_MGD_ID_KEY, mgd_identifiers)

        String validToken = validTokenJwtBuilder.compact()

        when: "get validation method is called"
        def tokenParser = new TokenParser()
        def output = tokenParser.retrieveTokenAttributes(validToken)

        then: "response says whether token is active"
        output.get(TokenParser.PRINCIPAL_ID_KEY) == expectedPrincipalId
        output.get(TokenParser.AUTO_ID_KEY) == expectedAutoId

        where:
        expirationDate                  | userId  | audience                    | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | TokenParser.OAB_FR_AUDIENCE | _
        VALID_TOKEN_EXPIRATION_DATE     | "54321" | TokenParser.OAB_FR_AUDIENCE | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | TokenParser.OAB_FR_AUDIENCE | _
        VALID_TOKEN_EXPIRATION_DATE     | null    | TokenParser.OAB_FR_AUDIENCE | _

        VALID_TOKEN_EXPIRATION_DATE     | "12345" | "invalid-audience"          | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | "invalid-audience"          | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | null                        | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | null                        | _

        INVALID_TOKEN_EXPIRATION_DATE   | "12345" | TokenParser.OAB_FR_AUDIENCE | _
        __
        mgd_identifiers             || expectedPrincipalId  || expectedAutoId
        exampleAutoIdSingleItem()   || "12345"              || "12345678-1c23-8a45-d1d2-02ba1742890s"
        exampleAutoIdMultiples()    || "54321"              || "12345678-1c23-8a45-d1d2-02ba1742890s,87643210-2d45-9b56-a1a6-02ba1123137b"
        emptyListOfMgdIdentifiers() || "12345"              || ""
        exampleAutoIdMultiples()    || null                 || null

        exampleAutoIdSingleItem()   || null                 || null
        emptyListOfMgdIdentifiers() || null                 || null
        exampleAutoIdSingleItem()   || null                 || null
        emptyListOfMgdIdentifiers() || null                 || null

        emptyListOfMgdIdentifiers() || null                 || null
    }

    def exampleAutoIdSingleItem() {
        List.of(String.format(AUTO_ID_URN, "12345678-1c23-8a45-d1d2-02ba1742890s"))
    }

    def exampleAutoIdMultiples() {
        List.of(String.format(AUTO_ID_URN, "12345678-1c23-8a45-d1d2-02ba1742890s"),
                String.format(AUTO_ID_URN, "87643210-2d45-9b56-a1a6-02ba1123137b"))
    }

    def emptyListOfMgdIdentifiers() {
        new ArrayList<>()
    }

    def "token parser extracts principal ID and SVCRM ID from token payload"() {

        given: "test token by user ID"
        // Test Token
        def subject = (userId != null)?String.format(PRINCIPAL_ID_URN, userId):null
        JwtBuilder validTokenJwtBuilder = new FrTokenTestHelper().buildBaseValidToken()
                .setSubject(subject)
                .setAudience(audience)
                .setExpiration(new SimpleDateFormat
                        ("yyyy-MM-dd").parse(expirationDate))

        validTokenJwtBuilder.claim(JWT_MGD_ID_KEY, mgd_identifiers)

        String validToken = validTokenJwtBuilder.compact()

        when: "get validation method is called"
        def tokenParser = new TokenParser()
        def output = tokenParser.retrieveTokenAttributes(validToken)

        then: "response says whether token is active"
        output.get(TokenParser.PRINCIPAL_ID_KEY) == expectedPrincipalId
        output.get(TokenParser.SVCRM_ID_KEY) == expectedSvcrmId

        where:
        expirationDate                  | userId  | audience                    | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | TokenParser.OAB_FR_AUDIENCE | _
        VALID_TOKEN_EXPIRATION_DATE     | "54321" | TokenParser.OAB_FR_AUDIENCE | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | TokenParser.OAB_FR_AUDIENCE | _
        VALID_TOKEN_EXPIRATION_DATE     | null    | TokenParser.OAB_FR_AUDIENCE | _

        VALID_TOKEN_EXPIRATION_DATE     | "12345" | "invalid-audience"          | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | "invalid-audience"          | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | null                        | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | null                        | _

        INVALID_TOKEN_EXPIRATION_DATE   | "12345" | TokenParser.OAB_FR_AUDIENCE | _
        __
        mgd_identifiers              || expectedPrincipalId  || expectedSvcrmId
        exampleSvcrmIdSingleItem()   || "12345"              || "12345678-1c23-8a45-d1d2-02ba1742890s"
        exampleSvcrmIdMultiples()    || "54321"              || "12345678-1c23-8a45-d1d2-02ba1742890s,87643210-2d45-9b56-a1a6-02ba1123137b"
        emptyListOfMgdIdentifiers()  || "12345"              || ""
        exampleSvcrmIdMultiples()    || null                 || null

        exampleSvcrmIdSingleItem()   || null                 || null
        emptyListOfMgdIdentifiers()  || null                 || null
        exampleSvcrmIdSingleItem()   || null                 || null
        emptyListOfMgdIdentifiers()  || null                 || null

        emptyListOfMgdIdentifiers()  || null                 || null
    }

    def exampleSvcrmIdSingleItem() {
        List.of(String.format(SVCRM_ID_URN, "12345678-1c23-8a45-d1d2-02ba1742890s", "primary"))
    }

    def exampleSvcrmIdMultiples() {
        List.of(String.format(SVCRM_ID_URN, "12345678-1c23-8a45-d1d2-02ba1742890s", "primary"),
                String.format(SVCRM_ID_URN, "87643210-2d45-9b56-a1a6-02ba1123137b", "secondary"))
    }

}