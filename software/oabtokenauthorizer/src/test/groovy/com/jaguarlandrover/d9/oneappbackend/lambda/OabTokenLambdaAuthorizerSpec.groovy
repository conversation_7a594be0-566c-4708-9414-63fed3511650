/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.jaguarlandrover.d9.oneappbackend.lambda.client.CachedAwsClient
import com.jaguarlandrover.d9.oneappbackend.lambda.client.FrApiClient
import com.jaguarlandrover.d9.oneappbackend.lambda.dto.FrJwtKey
import com.jaguarlandrover.d9.oneappbackend.lambda.dto.FrJwtKeyResponse
import com.jaguarlandrover.d9.oneappbackend.lambda.factory.ObjectFactory
import com.jaguarlandrover.d9.oneappbackend.lambda.helper.ApproovTokenTestHelper
import com.jaguarlandrover.d9.oneappbackend.lambda.helper.EnvironmentVariables
import com.jaguarlandrover.d9.oneappbackend.lambda.helper.FrTokenTestHelper
import com.jaguarlandrover.d9.oneappbackend.lambda.helper.SecretsManagerHelper
import com.jaguarlandrover.d9.oneappbackend.lambda.library.dto.AuthorizerOutput
import com.jaguarlandrover.d9.oneappbackend.lambda.library.dto.Statement
import com.jaguarlandrover.d9.oneappbackend.lambda.util.HttpRequestPreparer
import com.jaguarlandrover.d9.oneappbackend.lambda.util.PolicyDocumentCreator
import net.spy.memcached.MemcachedClient
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.core.LoggerContext
import org.apache.logging.log4j.core.appender.FileAppender
import org.apache.logging.log4j.core.config.xml.XmlConfiguration
import spock.lang.Specification

import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse

import static org.junit.jupiter.api.Assertions.*

class OabTokenLambdaAuthorizerSpec extends Specification {

    // API Gateway test values
    private static final String ACCOUNT_ID = "************"
    private static final String API_ID = "example-id-56052"
    private static final String STAGE = "dev"
    private static final String DENY = "Deny"
    private static final String ALLOW = "Allow"
    private static final String TRUE = "true"

    // /jwt_uri endpoint response with valid certificate
    private static final String VALID_CERT_FILE_NAME = "/response/fr-right-certificate.json"
    private static final String IN_VALID_CERT_FILE_NAME = "/response/fr-invalid-certificate.json"

    // Test token
    private static final String PRINCIPAL_ID_URN = "urn:iam2-mgd-v1:mgd-identifiers:oneapp-rangerover:auto-id:principal-uuid:%s"
    private static final String VALID_USER_ID = "12345"
    private static final String VALID_SUB = (VALID_USER_ID != null) ? String.format(PRINCIPAL_ID_URN, VALID_USER_ID) : null
    private static final String VALID_TOKEN = new FrTokenTestHelper().buildValidTokenBySubject(VALID_SUB)
    private static final String INVALID_TOKEN = new FrTokenTestHelper().buildValidTokenBySubject("1234567890")

    private static final String CERT_SEPERATOR = "@certSeperator";
    private static final String AUTH_HEADER = "authorization"
    private static final String CAP_AUTH_HEADER = "authorization"
    private static final String WRONG_AUTH_HEADER = "authorizations"
    private static final String MATCHING_FORGEROCK_HOST = "http://localhost:8888"
    private static final String POLICY_ARN_FORMAT = "arn:aws:execute-api:%s:%s:%s/%s/%s/%s"
    private static final String NOT_MATCHING_FORGEROCK_HOST = "identity.jaguarlandrover.com"
    private static final String LOG_FILE_PATTERN = "target/classes/logfile_%s.log"

    ObjectMapper mapper = new ObjectMapper()

    def "certificate service checks authorization of valid tokens with certificate cached in Memcached"() {

        given: "object factory"
        ObjectFactory objectFactory = Mock()

        and: "environment variable"
        EnvironmentVariables environmentVariables = Mock()
        environmentVariables.getVariable("_X_AMZN_TRACE_ID") >> "Test"
        environmentVariables.getVariable("FORGEROCK_HOST") >> MATCHING_FORGEROCK_HOST

        and: "forgerock client"
        HttpRequestPreparer httpRequestPreparer = Mock()
        HttpClient httpClient = Mock()
        FrApiClient frApiClient = new FrApiClient(httpRequestPreparer, httpClient)

        and: "memcached client"
        MemcachedClient memcachedClient = Mock()
        CachedAwsClient cachedAwsClient = Mock()
        cachedAwsClient.getClient() >> memcachedClient

        and: "policy document creator"
        PolicyDocumentCreator creator = new PolicyDocumentCreator(POLICY_ARN_FORMAT)

        and: "object factory creates dependencies"
        objectFactory.frApiClient() >> frApiClient
        objectFactory.policyDocumentCreator() >> creator
        objectFactory.cachedAwsClient() >> cachedAwsClient
        objectFactory.environmentVariables() >> environmentVariables

        and: "object mapper read mock response from /jwk_uri endpoint"
        def frJwtResponse = validFrResponse()

        and: "mock certificate from mock response"
        def certificate = validFrCertificate(frJwtResponse)
        cachedAwsClient.getKey(_, _) >> certificate

        and: "we have a mock request prepared for us"
        def mockRequest = Mock(HttpRequest)
        httpRequestPreparer.create() >> mockRequest

        and: "mock the response for the request"
        def mockResponse = Mock(HttpResponse)
        mockResponse.body() >> new ObjectMapper().writeValueAsString(frJwtResponse)
        httpClient.send(_, _) >> mockResponse

        and: "we have a ready request prepared for us"
        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent()
        if (token != null) {
            request.setHeaders(Map.of("Content-Type", "application/json"))
            request.setHeaders(Map.of(authHeader, token))
        }

        and: "http client responds with a response for the request"
        APIGatewayProxyRequestEvent.ProxyRequestContext context = new APIGatewayProxyRequestEvent.ProxyRequestContext()
        context.setAccountId(ACCOUNT_ID)
        context.setApiId(API_ID)
        context.setStage(STAGE)
        context.setHttpMethod("GET")
        request.setRequestContext(context)

        when: "get validation method is called"
        OabTokenLambdaAuthorizer authorizer = new OabTokenLambdaAuthorizer(objectFactory)
        AuthorizerOutput result = authorizer.handleRequest(request, null)

        then: "response says whether token is active"
        assertNotNull(result)
        assertEquals(expectedPrincipalId, result.getPrincipalId())

        def policyDocumentNode = mapper.readTree(mapper.writeValueAsString(result.getPolicyDocument()))
        policyDocumentNode.get("Version").asText() == "2012-10-17"
        def statements = policyDocumentNode.get("Statement") as List<Statement>
        def firstStatementNode = mapper.readTree(mapper.writeValueAsString(statements[0]))
        firstStatementNode.get("Action").asText() == expectedAction
        firstStatementNode.get("Effect").asText() == expectedEffect

        where:
        authHeader          | token                       || expectedPrincipalId    | expectedAction       | expectedEffect  |_
        AUTH_HEADER         | VALID_TOKEN                 || VALID_USER_ID          | "execute-api:Invoke" | "Allow"         |_
        AUTH_HEADER         | "Bearer " + VALID_TOKEN     || VALID_USER_ID          | "execute-api:Invoke" | "Allow"         |_
        AUTH_HEADER         | INVALID_TOKEN               || null                   | "execute-api:Invoke" | "Deny"          |_
        AUTH_HEADER         | "Bearer " + INVALID_TOKEN   || null                   | "execute-api:Invoke" | "Deny"          |_

        CAP_AUTH_HEADER     | VALID_TOKEN                 || VALID_USER_ID          | "execute-api:Invoke" | "Allow"         |_
        CAP_AUTH_HEADER     | "Bearer " + VALID_TOKEN     || VALID_USER_ID          | "execute-api:Invoke" | "Allow"         |_
        CAP_AUTH_HEADER     | INVALID_TOKEN               || null                   | "execute-api:Invoke" | "Deny"          |_
        CAP_AUTH_HEADER     | "Bearer " + INVALID_TOKEN   || null                   | "execute-api:Invoke" | "Deny"          |_
    }

    def "certificate service checks authorization of valid tokens with certificate from Forgerock client"() {

        given: "object factory"
        ObjectFactory objectFactory = Mock()

        and: "environment variable"
        EnvironmentVariables environmentVariables = Mock()
        environmentVariables.getVariable("_X_AMZN_TRACE_ID") >> null
        environmentVariables.getVariable("FORGEROCK_HOST") >> MATCHING_FORGEROCK_HOST

        and: "forgerock client"
        HttpRequestPreparer httpRequestPreparer = Mock()
        HttpClient httpClient = Mock()
        FrApiClient frApiClient = new FrApiClient(httpRequestPreparer, httpClient)

        and: "memcached client"
        MemcachedClient memcachedClient = null
        CachedAwsClient cachedAwsClient = Mock()
        cachedAwsClient.getClient() >> memcachedClient

        and: "object factory creates dependencies"
        objectFactory.frApiClient() >> frApiClient
        objectFactory.policyDocumentCreator() >> new PolicyDocumentCreator(POLICY_ARN_FORMAT)
        objectFactory.cachedAwsClient() >> cachedAwsClient
        objectFactory.environmentVariables() >> environmentVariables

        and: "object mapper read mock response from /jwk_uri endpoint"
        def frJwtResponse = validFrResponse()

        and: "mock certificate from mock response"
        cachedAwsClient.getKey(_, _) >> null
        frApiClient.getFrTokenResponse() >> frJwtResponse

        and: "we have a mock request prepared for us"
        def mockRequest = Mock(HttpRequest)
        httpRequestPreparer.create() >> mockRequest

        and: "mock the response for the request"
        def mockResponse = Mock(HttpResponse)
        mockResponse.body() >> new ObjectMapper().writeValueAsString(frJwtResponse)
        httpClient.send(_, _) >> mockResponse

        and: "we have a ready request prepared for us"
        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent()
        if (token != null) {
            request.setHeaders(Map.of("Content-Type", "application/json"))
            request.setHeaders(Map.of(authHeader, token))
        }

        and: "http client responds with a response for the request"
        APIGatewayProxyRequestEvent.ProxyRequestContext context = new APIGatewayProxyRequestEvent.ProxyRequestContext()
        context.setAccountId(ACCOUNT_ID)
        context.setApiId(API_ID)
        context.setStage(STAGE)
        context.setHttpMethod("GET")
        request.setRequestContext(context)

        when: "get validation method is called"
        OabTokenLambdaAuthorizer authorizer = new OabTokenLambdaAuthorizer(objectFactory)
        AuthorizerOutput result = authorizer.handleRequest(request, null)

        then: "response says whether token is active"
        assertNotNull(result)
        assertEquals(expectedPrincipalId, result.getPrincipalId())

        def policyDocumentNode = mapper.readTree(mapper.writeValueAsString(result.getPolicyDocument()))
        policyDocumentNode.get("Version").asText() == "2012-10-17"
        def statements = policyDocumentNode.get("Statement") as List<Statement>
        def firstStatementNode = mapper.readTree(mapper.writeValueAsString(statements[0]))
        firstStatementNode.get("Action").asText() == expectedAction
        firstStatementNode.get("Effect").asText() == expectedEffect

        where:
        authHeader          | token                         || expectedPrincipalId  | expectedAction       | expectedEffect || _
        AUTH_HEADER         | VALID_TOKEN                   || VALID_USER_ID        | "execute-api:Invoke" | "Allow"        || _
        AUTH_HEADER         | "Bearer " + VALID_TOKEN       || VALID_USER_ID        | "execute-api:Invoke" | "Allow"        || _
        AUTH_HEADER         | INVALID_TOKEN                 || null                 | "execute-api:Invoke" | "Deny"        || _
        AUTH_HEADER         | "Bearer " + INVALID_TOKEN     || null                 | "execute-api:Invoke" | "Deny"        || _

        CAP_AUTH_HEADER     | VALID_TOKEN                   || VALID_USER_ID        | "execute-api:Invoke" | "Allow"        || _
        CAP_AUTH_HEADER     | "Bearer " + VALID_TOKEN       || VALID_USER_ID        | "execute-api:Invoke" | "Allow"        || _
        CAP_AUTH_HEADER     | INVALID_TOKEN                 || null                 | "execute-api:Invoke" | "Deny"        || _
        CAP_AUTH_HEADER     | "Bearer " + INVALID_TOKEN     || null                 | "execute-api:Invoke" | "Deny"        || _
    }

    def "lambda authorizer checks authorization of tokens with invalid certificate from Memcached"() {
        given: "object factory"
        ObjectFactory objectFactory = Mock()

        and: "environment variable"
        EnvironmentVariables environmentVariables = Mock()
        environmentVariables.getVariable("_X_AMZN_TRACE_ID") >> null
        environmentVariables.getVariable("FORGEROCK_HOST") >> MATCHING_FORGEROCK_HOST

        and: "forgerock client"
        HttpRequestPreparer httpRequestPreparer = Mock()
        HttpClient httpClient = Mock()
        FrApiClient frApiClient = new FrApiClient(httpRequestPreparer, httpClient)

        and: "memcached client"
        MemcachedClient memcachedClient = null
        CachedAwsClient cachedAwsClient = Mock()
        cachedAwsClient.getClient() >> memcachedClient

        and: "object factory creates dependencies"
        objectFactory.frApiClient() >> frApiClient
        objectFactory.policyDocumentCreator() >> new PolicyDocumentCreator(POLICY_ARN_FORMAT)
        objectFactory.cachedAwsClient() >> cachedAwsClient
        objectFactory.environmentVariables() >> environmentVariables

        and: "object mapper read mock response from /jwk_uri endpoint"
        def frJwtResponse = invalidFrResponse()

        and: "mock certificate from mock response"
        def certificate = validFrCertificate(frJwtResponse)
        cachedAwsClient.getKey(_, _) >> certificate

        and: "we have a mock request prepared for us"
        def mockRequest = Mock(HttpRequest)
        httpRequestPreparer.create() >> mockRequest

        and: "mock the response for the request"
        def mockResponse = Mock(HttpResponse)
        mockResponse.body() >> new ObjectMapper().writeValueAsString(frJwtResponse)
        httpClient.send(_, _) >> mockResponse

        and: "we have a ready request prepared for us"
        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent()
        if (token != null) {
            request.setHeaders(Map.of("Content-Type", "application/json"))
            request.setHeaders(Map.of(authHeader, token))
        }

        and: "http client responds with a response for the request"
        APIGatewayProxyRequestEvent.ProxyRequestContext context = new APIGatewayProxyRequestEvent.ProxyRequestContext()
        context.setAccountId(ACCOUNT_ID)
        context.setApiId(API_ID)
        context.setStage(STAGE)
        context.setHttpMethod("GET")
        request.setRequestContext(context)

        when: "lambda authorizer"
        OabTokenLambdaAuthorizer authorizer = new OabTokenLambdaAuthorizer(objectFactory)

        then: "runtime error thrown for unauthorized request"
        assertThrows RuntimeException, ()->authorizer.handleRequest(request, null), "Unauthorized"

        where:
        authHeader          | token                         || _
        AUTH_HEADER         | VALID_TOKEN                   || _
        AUTH_HEADER         | "Bearer " + VALID_TOKEN       || _
        AUTH_HEADER         | INVALID_TOKEN                 || _
        AUTH_HEADER         | "Bearer " + INVALID_TOKEN     || _

        CAP_AUTH_HEADER     | VALID_TOKEN                   || _
        CAP_AUTH_HEADER     | "Bearer " + VALID_TOKEN       || _
        CAP_AUTH_HEADER     | INVALID_TOKEN                 || _
        CAP_AUTH_HEADER     | "Bearer " + INVALID_TOKEN     || _
    }

    def "lambda authorizer checks invalid Approov / ForgeRock tokens"() {

        given: "object factory"
        ObjectFactory objectFactory = Mock()

        and: "environment variable"
        EnvironmentVariables environmentVariables = Mock()
        environmentVariables.getVariable("_X_AMZN_TRACE_ID") >> "Test"
        environmentVariables.getVariable("FORGEROCK_HOST") >> forgerockHost
        environmentVariables.getVariable("APPROOV_ENABLED") >> TRUE

        and: "forgerock client"
        HttpRequestPreparer httpRequestPreparer = Mock()
        HttpClient httpClient = Mock()
        FrApiClient frApiClient = new FrApiClient(httpRequestPreparer, httpClient)

        and: "memcached client"
        MemcachedClient memcachedClient = Mock()
        CachedAwsClient cachedAwsClient = Mock()
        cachedAwsClient.getClient() >> memcachedClient

        and: "policy document creator"
        PolicyDocumentCreator creator = new PolicyDocumentCreator(POLICY_ARN_FORMAT)

        and: "object factory creates dependencies"
        objectFactory.frApiClient() >> frApiClient
        objectFactory.policyDocumentCreator() >> creator
        objectFactory.cachedAwsClient() >> cachedAwsClient
        objectFactory.environmentVariables() >> environmentVariables

        and: "object mapper read mock response from /jwk_uri endpoint"
        def frJwtResponse = validFrResponse()

        and: "mock certificate from mock response"
        def certificate = validFrCertificate(frJwtResponse)
        cachedAwsClient.getKey(_, _) >> certificate

        and: "we have a mock request prepared for us"
        def mockRequest = Mock(HttpRequest)
        httpRequestPreparer.create() >> mockRequest

        and: "mock the response for the request"
        def mockResponse = Mock(HttpResponse)
        mockResponse.body() >> new ObjectMapper().writeValueAsString(frJwtResponse)
        httpClient.send(_, _) >> mockResponse

        and: "we have a ready request prepared for us"
        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent()
        if (frToken != null) {
            request.setHeaders(Map.of("Content-Type", "application/json"))
            request.setHeaders(Map.of(authHeader, frToken))
        }
        if (approovHeader != null && approovToken != null) {
            request.setHeaders(Map.of(approovHeader, approovToken, AUTH_HEADER, VALID_TOKEN))
        }

        and: "http client responds with a response for the request"
        APIGatewayProxyRequestEvent.ProxyRequestContext context = new APIGatewayProxyRequestEvent.ProxyRequestContext()
        context.setAccountId(ACCOUNT_ID)
        context.setApiId(API_ID)
        context.setStage(STAGE)
        context.setHttpMethod("GET")
        request.setRequestContext(context)

        when: "lambda authorizer"
        OabTokenLambdaAuthorizer authorizer = new OabTokenLambdaAuthorizer(objectFactory)

        then: "runtime error thrown for unauthorized request"
        assertThrows RuntimeException, ()->authorizer.handleRequest(request, null), "Unauthorized"

        where:
        authHeader          | frToken                     | forgerockHost
        AUTH_HEADER         | "Bearer" + VALID_TOKEN      | MATCHING_FORGEROCK_HOST
        AUTH_HEADER         | ""                          | MATCHING_FORGEROCK_HOST
        AUTH_HEADER         | null                        | MATCHING_FORGEROCK_HOST
        AUTH_HEADER         | "Bearer " + VALID_TOKEN     | NOT_MATCHING_FORGEROCK_HOST
        AUTH_HEADER         | VALID_TOKEN                 | NOT_MATCHING_FORGEROCK_HOST
        AUTH_HEADER         | VALID_TOKEN                 | MATCHING_FORGEROCK_HOST
        AUTH_HEADER         | VALID_TOKEN                 | MATCHING_FORGEROCK_HOST
        AUTH_HEADER         | VALID_TOKEN                 | MATCHING_FORGEROCK_HOST

        CAP_AUTH_HEADER     | "Bearer" + VALID_TOKEN      | MATCHING_FORGEROCK_HOST
        CAP_AUTH_HEADER     | ""                          | MATCHING_FORGEROCK_HOST
        CAP_AUTH_HEADER     | null                        | MATCHING_FORGEROCK_HOST
        CAP_AUTH_HEADER     | "Bearer " + VALID_TOKEN     | NOT_MATCHING_FORGEROCK_HOST
        CAP_AUTH_HEADER     | VALID_TOKEN                 | NOT_MATCHING_FORGEROCK_HOST
        CAP_AUTH_HEADER     | VALID_TOKEN                 | MATCHING_FORGEROCK_HOST
        CAP_AUTH_HEADER     | VALID_TOKEN                 | MATCHING_FORGEROCK_HOST
        CAP_AUTH_HEADER     | VALID_TOKEN                 | MATCHING_FORGEROCK_HOST

        WRONG_AUTH_HEADER   | VALID_TOKEN                 | MATCHING_FORGEROCK_HOST
        WRONG_AUTH_HEADER   | VALID_TOKEN                 | NOT_MATCHING_FORGEROCK_HOST
        __
        approovHeader       | approovToken                                      |_
        "attestation"       | ApproovTokenTestHelper.buildApproovValidToken()   | _
        "attestation"       | ApproovTokenTestHelper.buildApproovValidToken()   | _
        "attestation"       | ApproovTokenTestHelper.buildApproovValidToken()   | _
        "attestation"       | ApproovTokenTestHelper.buildApproovValidToken()   | _
        "attestation"       | ApproovTokenTestHelper.buildApproovValidToken()   | _
        "attestation"       | "invalid_approov_token"                           | _
        "attestation"       | ""                                                | _
        "non-attestation"   | "approov_token"                                   | _

        "attestation"       | ApproovTokenTestHelper.buildApproovValidToken()   | _
        "attestation"       | ApproovTokenTestHelper.buildApproovValidToken()   | _
        "attestation"       | ApproovTokenTestHelper.buildApproovValidToken()   | _
        "attestation"       | ApproovTokenTestHelper.buildApproovValidToken()   | _
        "attestation"       | ApproovTokenTestHelper.buildApproovValidToken()   | _
        "attestation"       | "invalid_approov_token"                           | _
        "attestation"       | ""                                                | _
        "non-attestation"   | "approov_token"                                   | _

        "attestation"       | ApproovTokenTestHelper.buildApproovValidToken()   | _
        "attestation"       | ApproovTokenTestHelper.buildApproovValidToken()   | _
    }

    def "lambda authorizer checks approov valid token"() {

        given: "object factory"
        ObjectFactory objectFactory = Mock()

        and: "environment variables has enabled approov"
        EnvironmentVariables environmentVariables = Mock()
        environmentVariables.getVariable("FORGEROCK_HOST") >> MATCHING_FORGEROCK_HOST
        environmentVariables.getVariable("APPROOV_ENABLED") >> TRUE

        and: "secrets manager helper"
        SecretsManagerHelper secretsManagerHelper = Mock()
        secretsManagerHelper.getApproovSecretKey() >> ApproovTokenTestHelper.encodedApproovSecretKeyString

        and: "forgerock client"
        HttpRequestPreparer httpRequestPreparer = Mock()
        HttpClient httpClient = Mock()
        FrApiClient frApiClient = new FrApiClient(httpRequestPreparer, httpClient)

        and: "memcached client"
        MemcachedClient memcachedClient = Mock()
        CachedAwsClient cachedAwsClient = Mock()
        cachedAwsClient.getClient() >> memcachedClient

        and: "policy document creator"
        PolicyDocumentCreator creator = new PolicyDocumentCreator(POLICY_ARN_FORMAT)

        and: "object factory creates dependencies"
        objectFactory.policyDocumentCreator() >> creator
        objectFactory.environmentVariables() >> environmentVariables
        objectFactory.secretsManagerHelper() >> secretsManagerHelper
        objectFactory.frApiClient() >> frApiClient
        objectFactory.cachedAwsClient() >> cachedAwsClient

        and: "object mapper read mock response from /jwk_uri endpoint"
        def frJwtResponse = validFrResponse()

        and: "mock certificate from mock response"
        def certificate = validFrCertificate(frJwtResponse)
        cachedAwsClient.getKey(_, _) >> certificate

        and: "we have a mock request prepared for us"
        def mockRequest = Mock(HttpRequest)
        httpRequestPreparer.create() >> mockRequest

        and: "mock the response for the request"
        def mockResponse = Mock(HttpResponse)
        mockResponse.body() >> new ObjectMapper().writeValueAsString(frJwtResponse)
        httpClient.send(_, _) >> mockResponse

        and: "we have both tokens in the request"
        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent()
        request.setHeaders(Map.of(approovHeader, approovToken, AUTH_HEADER, VALID_TOKEN))

        and: "we have a request context"
        APIGatewayProxyRequestEvent.ProxyRequestContext context = new APIGatewayProxyRequestEvent.ProxyRequestContext()
        context.setAccountId(ACCOUNT_ID)
        context.setApiId(API_ID)
        context.setStage(STAGE)
        context.setHttpMethod("GET")
        request.setRequestContext(context)

        when: "lambda handles request"
        OabTokenLambdaAuthorizer authorizer = new OabTokenLambdaAuthorizer(objectFactory)
        AuthorizerOutput result = authorizer.handleRequest(request, null)

        then: "response has a corresponding policy effect"
        def policyDocumentNode = mapper.readTree(mapper.writeValueAsString(result.getPolicyDocument()))
        def statements = policyDocumentNode.get("Statement") as List<Statement>
        def firstStatementNode = mapper.readTree(mapper.writeValueAsString(statements[0]))
        firstStatementNode.get("Effect").asText() == expectedPolicyEffect

        where:
        approovHeader     | approovToken                                       | expectedPolicyEffect
        "attestation"     | ApproovTokenTestHelper.buildApproovValidToken()    | ALLOW
    }

    def "request ID from API Gateway is injected into log"() {

        given: "we add a file appender to the root logger"
        def fileName = String.format(LOG_FILE_PATTERN, caseNo)
        System.setProperty("logFilename", fileName)

        LoggerContext loggerContext = (LoggerContext) LogManager.getContext(false)
        loggerContext.reconfigure()
        XmlConfiguration configuration = (XmlConfiguration) loggerContext.getConfiguration()
        FileAppender fileAppender = configuration.getAppender("JsonLogfileAppender")
        fileAppender.start()
        loggerContext.getConfiguration().addAppender(fileAppender)
        loggerContext.getRootLogger().addAppender(loggerContext.getConfiguration().getAppender(fileAppender.getName()))

        and: "object factory"
        ObjectFactory objectFactory = Mock()

        and: "environment variable"
        EnvironmentVariables environmentVariables = Mock()
        environmentVariables.getVariable("FORGEROCK_HOST") >> MATCHING_FORGEROCK_HOST

        and: "forgerock client"
        HttpRequestPreparer httpRequestPreparer = Mock()
        HttpClient httpClient = Mock()
        FrApiClient frApiClient = new FrApiClient(httpRequestPreparer, httpClient)

        and: "memcached client"
        MemcachedClient memcachedClient = Mock()
        CachedAwsClient cachedAwsClient = Mock()
        cachedAwsClient.getClient() >> memcachedClient

        and: "policy document creator"
        PolicyDocumentCreator creator = new PolicyDocumentCreator(POLICY_ARN_FORMAT)

        and: "object factory creates dependencies"
        objectFactory.frApiClient() >> frApiClient
        objectFactory.policyDocumentCreator() >> creator
        objectFactory.cachedAwsClient() >> cachedAwsClient
        objectFactory.environmentVariables() >> environmentVariables

        and: "object mapper read mock response from /jwk_uri endpoint"
        def frJwtResponse = (!"".equalsIgnoreCase(VALID_CERT_FILE_NAME)) ? mapper.readValue(this.getClass().getResource(VALID_CERT_FILE_NAME).text, FrJwtKeyResponse) : ""

        and: "mock certificate from mock response"
        def certificate = validFrCertificate(frJwtResponse)
        cachedAwsClient.getKey(_, _) >> certificate

        and: "we have a mock request prepared for us"
        def mockRequest = Mock(HttpRequest)
        httpRequestPreparer.create() >> mockRequest

        and: "mock the response for the request"
        def mockResponse = Mock(HttpResponse)
        mockResponse.body() >> new ObjectMapper().writeValueAsString(frJwtResponse)
        httpClient.send(_, _) >> mockResponse

        and: "we have a ready request prepared for us"
        APIGatewayProxyRequestEvent request = new APIGatewayProxyRequestEvent()
        request.setHeaders(Map.of("Content-Type", "application/json"))
        request.setHeaders(Map.of(AUTH_HEADER, VALID_TOKEN))

        and: "we have the request id in the context"
        request.setRequestContext(new APIGatewayProxyRequestEvent.ProxyRequestContext().withRequestId(requestId))

        and: "authorizer handle request method is called"
        OabTokenLambdaAuthorizer authorizer = new OabTokenLambdaAuthorizer(objectFactory)
        authorizer.handleRequest(request, null)

        when: "logs file is parsed into array of log objects"
        def logObjects = []
        def file = new File(fileName)
            file.eachLine { line ->
                JsonNode logEntry = mapper.readTree(line)
                def logObject = [
                        level         : logEntry.get("level")?.asText(),
                        loggerName    : logEntry.get("loggerName")?.asText(),
                        message       : logEntry.get("message")?.asText(),
                        httpRequestId : logEntry.get("http.request_id")?.asText()
                ]
                // converts the map log into a JSON string
                mapper.writeValueAsString(logObject)
                // only add logs with level "INFO" because ERROR dont have context request id value
                if ((logObject.level == "INFO") && (logObject.message.contains("Principal ID: "))) {
                    logObjects << logObject
                }
            }

        then: "each log object have an expected http.request_id value"
        logObjects.each { log ->
            assert log.httpRequestId == expectedRequestId
        }

        where:
        caseNo      | requestId     || expectedRequestId    | _
        "0"         | "1234567890"  || "1234567890"         | _
        "1"         | ""            || null                 | _

    }

    def validFrResponse() {
        (!"".equalsIgnoreCase(VALID_CERT_FILE_NAME)) ? mapper.readValue(this.getClass().getResource(VALID_CERT_FILE_NAME).text, FrJwtKeyResponse) : ""
    }

    def invalidFrResponse() {
        (!"".equalsIgnoreCase(IN_VALID_CERT_FILE_NAME)) ? mapper.readValue(this.getClass().getResource(IN_VALID_CERT_FILE_NAME).text, FrJwtKeyResponse) : ""
    }

    def validFrCertificate(frJwtResponse) {
        def certificate = ""
        if (frJwtResponse instanceof FrJwtKeyResponse) {
            FrJwtKeyResponse response = (FrJwtKeyResponse) frJwtResponse
            StringBuilder stringBuilder = new StringBuilder()
            List<FrJwtKey> keys = response.getKeys()
            for (int i = 0, size = keys.size(); i < size; i++) {
                stringBuilder.append(keys.get(i).getX5c().get(0))
                if (i<size-1){
                    stringBuilder.append(CERT_SEPERATOR)
                }
            }
            certificate =  stringBuilder.toString()
        }
        certificate
    }


}
