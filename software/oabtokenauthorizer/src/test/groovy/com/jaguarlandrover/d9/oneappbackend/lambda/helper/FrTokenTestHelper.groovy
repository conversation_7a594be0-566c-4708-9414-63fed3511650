/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.helper

import io.jsonwebtoken.Jwts

import java.security.KeyFactory
import java.security.NoSuchAlgorithmException
import java.security.PrivateKey
import java.security.spec.InvalidKeySpecException
import java.security.spec.PKCS8EncodedKeySpec

class FrTokenTestHelper {

    // FR JWT settings
    static final int EXPIRATION_IN_SECONDS = 120000
    public static final VALID_TEST_ISS = "http://localhost:8888/gateway/oauth2/realms/root/realms/customer"
    private static final String privateKeyFileName = "/private.pem"
    private static final Date issueDate = new Date()

    static def buildBaseValidToken() {
        return Jwts.builder()
                   .setIssuedAt(issueDate)
                   .setExpiration(new Date(issueDate.getTime() + EXPIRATION_IN_SECONDS))
                   .signWith(privateKey)
    }

    static def buildValidTokenBySubject(String subject) {
        return buildBaseValidToken()
                .setSubject(subject)
                .setAudience(TokenParser.OAB_FR_AUDIENCE)
                .setIssuer(VALID_TEST_ISS)
                .compact()
    }

    private static def getPrivateKey() throws NoSuchAlgorithmException, InvalidKeySpecException {
        String privateKeyStr = FrTokenTestHelper.getResource(privateKeyFileName).text
        privateKeyStr = privateKeyStr.replace("-----BEGIN PRIVATE KEY-----", "")
        privateKeyStr = privateKeyStr.replace("-----END PRIVATE KEY-----", "")
        privateKeyStr = privateKeyStr.replaceAll("\n", "")

        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKeyStr))
        KeyFactory kf = KeyFactory.getInstance("RSA")
        PrivateKey privKey = kf.generatePrivate(keySpec)
        return privKey
    }
}
