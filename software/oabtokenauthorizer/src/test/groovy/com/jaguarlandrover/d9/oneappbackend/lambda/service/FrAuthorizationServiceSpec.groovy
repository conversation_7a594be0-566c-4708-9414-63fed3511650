/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.service

import com.jaguarlandrover.d9.oneappbackend.lambda.helper.EnvironmentVariables
import com.jaguarlandrover.d9.oneappbackend.lambda.helper.FrTokenTestHelper
import com.jaguarlandrover.d9.oneappbackend.lambda.helper.TokenParser
import io.jsonwebtoken.JwtBuilder
import spock.lang.Specification

import java.text.SimpleDateFormat

class FrAuthorizationServiceSpec extends Specification {

    static final String PRINCIPAL_ID_URN = "urn:iam2-mgd-v1:mgd-identifiers:oneapp-rangerover:auto-id:principal-uuid:%s"

    static final String VALID_TOKEN_EXPIRATION_DATE = "3023-12-31"
    static final String INVALID_TOKEN_EXPIRATION_DATE = "2023-01-31"

    static final String RIGHT_CERTIFICATE = "MIIDPzCCAicCFDaux5Nqp7A3u9lAggNdBGiRjJgKMA0GCSqGSIb3DQEBCwUAMFwxCzAJBgNVBAYTAkdCMRMwEQYDVQQIDApNYW5jaGVzdGVyMQwwCgYDVQQKDANKTFIxKjAoBgkqhkiG9w0BCQEWG2ZjaGVuZzFAamFndWFybGFuZHJvdmVyLmNvbTAeFw0yMzAyMjExMTQwMzRaFw0yNDAyMjExMTQwMzRaMFwxCzAJBgNVBAYTAkdCMRMwEQYDVQQIDApNYW5jaGVzdGVyMQwwCgYDVQQKDANKTFIxKjAoBgkqhkiG9w0BCQEWG2ZjaGVuZzFAamFndWFybGFuZHJvdmVyLmNvbTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAMenrDCTh5O5FO8aLZxyW0zaflUttzdM/ji0sTpNBbiYnFFvC+Dw+oeIAEHV213AsgvwNgF5EZ/MGFANSFDBWKejNuHOhcATI2nUSKpKQCgI6h9glwYJSxTuDfKhRwdfs9szmfMxed5qdi1B9pc3Mdkc4MwLd7COv93QKn7pZ/kCi0/A5xaEKHjSd7lGEF0a9sfqQ3xVyZYg+8Nlf/wfPn0qZ1+WrG+VEVuU87siawk2NOlVbACa0ycZf0HB7Fr2zz0b0fO8drVsjqJIzbH5KDa6PvFuOg9LWfJzUtLl5AARCVhLDnTX+qXRcX8oYYQ2QTbLL6CDc4be8SSLYNIHeGcCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAUNff3yk/5PBdScIVsUbqI2oSCluGrsSXUmC5KST+rXN+NE38p3a5RUTTQ+Srl57my5RspdP/i+Ylz5zIcQWBtcGxH3RAT8SjRJ6sLA5PCLaeQaLQvEOqZ++1ImSP9BhFyouQtV7zHWjWNUep8tqykDHdCXNLkvQy7BFnxaVvRVMO0sLRlHnB+NNOVEk/XCOili17f8Yqa+fdtpROXn3hkd4iWJAKgXYPnfuFiilj4lJz4uw+ePB3u7EIR4XQjU96SM2xmSaV3lzAMtlZDrIadskEpSK9bdkJC/8ftJmdFEAmOd+fAegVofvr/tdyWKCbsHN9DJXgvNTKl/GOLxfGWQ=="
    static final String WRONG_CERTIFICATE = "MIIC8DCCAdgCCQDfzHxquGqBiTANBgkqhkiG9w0BAQsFADA6MQswCQYDVQQGEwJERTEQMA4GA1UEBwwHR2VybWFueTEZMBcGA1UEAwwQc2VydmljZWxheWVycy5pbzAeFw0yMjAyMTUxNDIxNTBaFw0yNzAyMTQxNDIxNTBaMDoxCzAJBgNVBAYTAkRFMRAwDgYDVQQHDAdHZXJtYW55MRkwFwYDVQQDDBBzZXJ2aWNlbGF5ZXJzLmlvMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAphWxfZscdXfKa7q5ZQoqWNvXj0NuUmPT17EDUtTywI/w14H4mwwcFBhaFcPTKx/8TmWDXsawj8wxRr3zL9AVRkp5vSuexLdhIfyLiq+uupwZDUfSptiDTZiCr7V0FwzusoNyQD8a0EDpkM1zJSqCNr7SBTAButx5ZD/r8xEIYi4xcL2UZemgoBNEsY8e/R1R5kt5aTn2+QmgeqQriJovjofdDl7M2taZjn6QXNtLT4JRCtk+uLIzbyNZXREDvsO6c5f2F/rQNrovYt1hg3BLrb7liehGc5xUg9xEvFADxFEHPtZalqxUTX5wluLj/fZyZtdXqdo4RS4lNxCRAfvgFQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQAUfXId9hRu2C60oPTKUlQysyHSxGhzRepmpkW4FhlZEng3FiVQaWKHodR45Z2ef+i2NI+bF3C5ptWTj7pdtusZm8hqTRU5++2nN0xg5zhHTldz/ugK1qzmopnv9ZCqdzJhP6OWPVIJWl+GftIM3qfp768i8MFUh57EpSw2CkLI3bt0rF5KCLfAhhj9zhBdR0afOIQeb1nGdbXZeGzZjCUy3rzRqNRC8dki9KBAfkvrtVIzuRaCr8pSCG2DTY1TzVqQIurnQ2D5r9PVkBnbZuS5Witul+fEokfJS86Aq8lR+kqWfrSFL30v2McQQBFzaT26Vsmk0g/PYyx+lr67rMaa"
    static final String INVALID_CERTIFICATE = "12345"

    static final String MATCHING_FORGEROCK_HOST = "http://localhost:8888"
    static final String NOT_MATCHING_FORGEROCK_HOST = "identity.jaguarlandrover.com"

    static final VALID_TEST_ISS = "http://localhost:8888/gateway/oauth2/realms/root/realms/customer"

    def "validates token"() {
        given: "test token by user ID"
        // Test Token
        def subject = (userId != null)?String.format(PRINCIPAL_ID_URN, userId):null
        JwtBuilder validTokenJwtBuilder = new FrTokenTestHelper().buildBaseValidToken()
                .setSubject(subject)
                .setAudience(audience)
                .setIssuer(VALID_TEST_ISS)
                .setExpiration(new SimpleDateFormat
                        ("yyyy-MM-dd").parse(expirationDate))

        String validToken = validTokenJwtBuilder.compact()

        and: "environment variable"
        def environmentVariables = Mock(EnvironmentVariables)
        environmentVariables.getVariable("FORGEROCK_HOST") >> forgerockHost

        when: "isTokenValid method in AuthorizationService is called"
        def frAuthorizationService = new FrAuthorizationService()
        def output = frAuthorizationService.isTokenValid(validToken, certificate, environmentVariables)

        then: "check whether the given token is valid or not"
        output == expectedResult

        where:
        expirationDate                  | userId  | audience                    | certificate           | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | TokenParser.OAB_FR_AUDIENCE | RIGHT_CERTIFICATE     | _
        VALID_TOKEN_EXPIRATION_DATE     | "54321" | TokenParser.OAB_FR_AUDIENCE | RIGHT_CERTIFICATE     | _
        VALID_TOKEN_EXPIRATION_DATE     | null    | TokenParser.OAB_FR_AUDIENCE | RIGHT_CERTIFICATE     | _

        VALID_TOKEN_EXPIRATION_DATE     | "12345" | TokenParser.OAB_FR_AUDIENCE | WRONG_CERTIFICATE     | _

        VALID_TOKEN_EXPIRATION_DATE     | "12345" | "invalid-audience"          | RIGHT_CERTIFICATE     | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | "invalid-audience"          | RIGHT_CERTIFICATE     | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | null                        | RIGHT_CERTIFICATE     | _
        VALID_TOKEN_EXPIRATION_DATE     | "12345" | null                        | RIGHT_CERTIFICATE     | _

        INVALID_TOKEN_EXPIRATION_DATE   | "12345" | TokenParser.OAB_FR_AUDIENCE | RIGHT_CERTIFICATE     | _
        INVALID_TOKEN_EXPIRATION_DATE   | "12345" | TokenParser.OAB_FR_AUDIENCE | INVALID_CERTIFICATE   | _

        VALID_TOKEN_EXPIRATION_DATE     | "12345" | TokenParser.OAB_FR_AUDIENCE | RIGHT_CERTIFICATE     | _
        VALID_TOKEN_EXPIRATION_DATE     | "54321" | TokenParser.OAB_FR_AUDIENCE | RIGHT_CERTIFICATE     | _
        __
        forgerockHost               || expectedResult
        MATCHING_FORGEROCK_HOST     || true
        MATCHING_FORGEROCK_HOST     || true
        MATCHING_FORGEROCK_HOST     || false

        MATCHING_FORGEROCK_HOST     || false

        MATCHING_FORGEROCK_HOST     || false
        MATCHING_FORGEROCK_HOST     || false
        MATCHING_FORGEROCK_HOST     || false
        MATCHING_FORGEROCK_HOST     || false

        NOT_MATCHING_FORGEROCK_HOST || false
        NOT_MATCHING_FORGEROCK_HOST || false

        NOT_MATCHING_FORGEROCK_HOST || false
        NOT_MATCHING_FORGEROCK_HOST || false
    }

    def "check the validation of issuer claim"() {
        given: "Forgerock authorization service"
        def frAuthorizationService = new FrAuthorizationService()

        when: "isIssuerClaimValid is called"
        def result = frAuthorizationService.isIssuerClaimValid(iss, forgerockHost)

        then: "the result should be as expected"
        result == expectedResult

        where:
        iss                       | forgerockHost               | expectedResult
        VALID_TEST_ISS            | MATCHING_FORGEROCK_HOST     | true
        VALID_TEST_ISS            | NOT_MATCHING_FORGEROCK_HOST | false
        "http://localhost:3000"   | NOT_MATCHING_FORGEROCK_HOST | false
        null                      | MATCHING_FORGEROCK_HOST     | false
        ""                        | MATCHING_FORGEROCK_HOST     | false
    }

}