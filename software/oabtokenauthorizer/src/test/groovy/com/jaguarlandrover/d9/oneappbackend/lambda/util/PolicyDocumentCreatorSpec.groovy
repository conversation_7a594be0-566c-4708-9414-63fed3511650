/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.util

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent
import com.fasterxml.jackson.databind.ObjectMapper
import com.jaguarlandrover.d9.oneappbackend.lambda.library.dto.Statement
import spock.lang.Specification

class PolicyDocumentCreatorSpec extends Specification {

    PolicyDocumentCreator creator = new PolicyDocumentCreator("arn:aws:execute-api:%s:%s:%s/%s/%s/%s")
    ObjectMapper objectMapper = new ObjectMapper()

    def "creates policy document with given values"() {

        given: "some effect"
        def effect = "some effect"

        and: "request context inside request event"
        def context = Mock(APIGatewayProxyRequestEvent.ProxyRequestContext)
        def apiGatewayRequestEvent = Mock(APIGatewayProxyRequestEvent)
        apiGatewayRequestEvent.getRequestContext() >> context

        when: "asked to create policy document"
        def output = creator.createPolicyDocument(effect, apiGatewayRequestEvent)

        then: "it created it with correct values"
        def outputNode = objectMapper.readTree(objectMapper.writeValueAsString(output))
        outputNode.get("Version").asText() == "2012-10-17"
        def statements = outputNode.get("Statement") as List<Statement>
        def firstStatementNode = objectMapper.readTree(objectMapper.writeValueAsString(statements[0]))
        firstStatementNode.get("Action").asText() == "execute-api:Invoke"
        firstStatementNode.get("Effect").asText() == effect
        firstStatementNode.get("Resource").asText().contains("arn:aws:execute-api:")
    }

    def "if context null creates policy document with effect only"() {

        given: "some effect"
        def effect = "some effect"

        and: "request context inside request event"
        def apiGatewayRequestEvent = Mock(APIGatewayProxyRequestEvent)
        apiGatewayRequestEvent.getRequestContext() >> null

        when: "asked to create policy document"
        def output = creator.createPolicyDocument(effect, apiGatewayRequestEvent)

        then: "it created it with correct values"
        def outputNode = objectMapper.readTree(objectMapper.writeValueAsString(output))
        outputNode.get("Version").asText() == "2012-10-17"
        def statement = outputNode.get("Statement") as List<Statement>
        def statementNode = objectMapper.readTree(objectMapper.writeValueAsString(statement[0]))
        statementNode.get("Action").asText() == "execute-api:Invoke"
        statementNode.get("Effect").asText() == effect
        statementNode.get("Resource").asText() == ""
    }

}
