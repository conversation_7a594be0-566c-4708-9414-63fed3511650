/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.client

import net.spy.memcached.MemcachedClient
import spock.lang.Specification

class CachedAwsClientSpec extends Specification {

    OabMemcachedClient oabMemcachedClient = new OabMemcachedClient()

    def "Initializes client correctly"() {

        when: "initialize method is called"
        oabMemcachedClient.initializeMemcachedClient("localhost", 8888)

        then: "client is initialized"
        oabMemcachedClient.client != null
    }

    def "forwards get method to the contained client"() {
        given: "internal client is set"
        MemcachedClient memcachedClient = Mock()
        oabMemcachedClient.memcachedClient = memcachedClient

        when: "we call get"
        oabMemcachedClient.asyncGet("key")

        then: "client method was called"
        1 *  memcachedClient.asyncGet("key")
    }

    def "forwards set method to the contained client"() {
        given: "internal client is set"
        MemcachedClient memcachedClient = Mock()
        oabMemcachedClient.memcachedClient = memcachedClient

        when: "we call set"
        oabMemcachedClient.set("key", 1, "value")

        then: "client method was called"
        1 *  memcachedClient.set("key", 1, "value")
    }

    def "returns contained client"() {

        given: "internal client is set"
        MemcachedClient memcachedClient = Mock()
        oabMemcachedClient.memcachedClient = memcachedClient

        when: "we ask for the client"
        def output = oabMemcachedClient.getClient()

        then: "client is returned"
        output == memcachedClient
    }
}
