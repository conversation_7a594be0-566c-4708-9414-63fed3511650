/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.factory

import com.jaguarlandrover.d9.oneappbackend.lambda.client.OabMemcachedClient
import com.jaguarlandrover.d9.oneappbackend.lambda.helper.EnvironmentVariables
import com.jaguarlandrover.d9.oneappbackend.lambda.helper.SecretsManagerHelper
import spock.lang.Specification

class ObjectFactorySpec extends Specification {

    ObjectFactory objectFactory = new ObjectFactory(new EnvironmentVariables(), new OabMemcachedClient(), new SecretsManagerHelper())

    def "objectFactory instantiates objects correctly"() {

        given: "objectFactory"
        objectFactory

        when: "get environment variable"
        def variables = objectFactory.environmentVariables()

        then: "it's an instance of EnvironmentVariables"
        assert variables instanceof EnvironmentVariables
    }


    // TODO: test all the methods in object factory
}
