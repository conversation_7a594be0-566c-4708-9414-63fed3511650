/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent
import com.jaguarlandrover.d9.oneappbackend.lambda.helper.ApproovTokenTestHelper

import static com.jaguarlandrover.d9.oneappbackend.lambda.helper.FrTokenTestHelper.buildValidTokenBySubject

class OabTokenLambdaAuthorizerSpecIT extends AbstractSpecificationIT {

    final String PRINCIPAL_ID_URN = "urn:iam2-mgd-v1:mgd-identifiers:oneapp-rangerover:auto-id:principal-uuid:%s"

    OabTokenLambdaAuthorizer oabTokenLambdaAuthorizer =
            new OabTokenLambdaAuthorizer(environmentVariables, oabMemcachedClient, secretsManagerHelper)

    def setupSpec() {
        'prepare ForgeRock server for testing'()
        'mock environmental variables'()
        'mock memcached behaviour'()
        'mock secrets manager helper'()
    }

    def setup() {
        forgeRockServer.start()
    }

    def cleanup() {
        forgeRockServer.resetAll()
        forgeRockServer.stop()
    }

    def "authorizer checks validity of the FR and Approov tokens"() {

        given: "valid tokens"
        def VALID_SUB = (VALID_USER_ID != null) ? String.format(PRINCIPAL_ID_URN, VALID_USER_ID) : null
        def validFrToken = buildValidTokenBySubject(VALID_SUB)
        def validApproovToken = ApproovTokenTestHelper.buildApproovValidToken()

        and: "and the request contains the token"
        def request = new APIGatewayProxyRequestEvent()
        request.setHeaders(Map.of(CONTENT_TYPE_HEADER, "application/json"))
        request.setHeaders(Map.of(AUTH_HEADER, validFrToken, ATTESTATION_HEADER, validApproovToken))

        when: "it is sent to the authorizer"
        def output = oabTokenLambdaAuthorizer.handleRequest(request, prepareContextForTest())

        then: "the response says that the token is valid"
        output != null
        output.policyDocument.Statement[0].Effect == "Allow"
    }
}