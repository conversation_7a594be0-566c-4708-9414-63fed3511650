/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.jaguarlandrover.d9.oneappbackend.lambda.client.CachedAwsClient
import com.jaguarlandrover.d9.oneappbackend.lambda.client.FrApiClient
import com.jaguarlandrover.d9.oneappbackend.lambda.dto.FrJwtKey
import com.jaguarlandrover.d9.oneappbackend.lambda.dto.FrJwtKeyResponse
import com.jaguarlandrover.d9.oneappbackend.lambda.util.HttpRequestPreparer
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse
import net.spy.memcached.MemcachedClient
import spock.lang.Specification

import java.util.concurrent.ExecutionException
import java.util.concurrent.TimeoutException

class CertificateServiceSpec extends Specification {

    ObjectMapper mapper = new ObjectMapper()
    String CERT_SEPERATOR = "@certSeperator";

    def "Get Certificate from ForgeRock Server"() {
        given: "object mapper read mock response from /jwk_uri endpoint"
        def frJwtKeyResponse = (!"".equalsIgnoreCase(keyFileName)) ? mapper.readValue(this.getClass().getResource(keyFileName).text, FrJwtKeyResponse) : ""

        and: "we have a mock request prepared for us"
        def httpRequestPreparer = Mock(HttpRequestPreparer)
        def mockRequest = Mock(HttpRequest)
        httpRequestPreparer.create() >> mockRequest

        and: "forgerock client"
        def httpClient = Mock(HttpClient)
        def frApiClient = new FrApiClient(httpRequestPreparer, httpClient)

        and: "memcached client is not available"
        //def memcachedClient = Mock(MemcachedClient)
        def cachedAwsClient = Mock(CachedAwsClient)
        cachedAwsClient.getClient() >> null

        and: "mock the response for the request to ForgeRock Server"
        def mockResponse = Mock(HttpResponse)
        mockResponse.body() >> new ObjectMapper().writeValueAsString(frJwtKeyResponse)
        httpClient.send(_, _) >> mockResponse

        when: "get certificate method is called"
        def certificateService = new CertificateService(frApiClient, cachedAwsClient)
        def output = certificateService.getCertificate()

        then: "response says whether token is active"
        output == expectedCertificate

        and: "certificate is stored in Memcached"
        1 * cachedAwsClient.setKey(_, expectedCertificate, _)

        where:
        keyFileName                             || expectedCertificate
        "/response/fr-right-certificate.json"   || "12345@certSeperatorMIIDPzCCAicCFDaux5Nqp7A3u9lAggNdBGiRjJgKMA0GCSqGSIb3DQEBCwUAMFwxCzAJBgNVBAYTAkdCMRMwEQYDVQQIDApNYW5jaGVzdGVyMQwwCgYDVQQKDANKTFIxKjAoBgkqhkiG9w0BCQEWG2ZjaGVuZzFAamFndWFybGFuZHJvdmVyLmNvbTAeFw0yMzAyMjExMTQwMzRaFw0yNDAyMjExMTQwMzRaMFwxCzAJBgNVBAYTAkdCMRMwEQYDVQQIDApNYW5jaGVzdGVyMQwwCgYDVQQKDANKTFIxKjAoBgkqhkiG9w0BCQEWG2ZjaGVuZzFAamFndWFybGFuZHJvdmVyLmNvbTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAMenrDCTh5O5FO8aLZxyW0zaflUttzdM/ji0sTpNBbiYnFFvC+Dw+oeIAEHV213AsgvwNgF5EZ/MGFANSFDBWKejNuHOhcATI2nUSKpKQCgI6h9glwYJSxTuDfKhRwdfs9szmfMxed5qdi1B9pc3Mdkc4MwLd7COv93QKn7pZ/kCi0/A5xaEKHjSd7lGEF0a9sfqQ3xVyZYg+8Nlf/wfPn0qZ1+WrG+VEVuU87siawk2NOlVbACa0ycZf0HB7Fr2zz0b0fO8drVsjqJIzbH5KDa6PvFuOg9LWfJzUtLl5AARCVhLDnTX+qXRcX8oYYQ2QTbLL6CDc4be8SSLYNIHeGcCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAUNff3yk/5PBdScIVsUbqI2oSCluGrsSXUmC5KST+rXN+NE38p3a5RUTTQ+Srl57my5RspdP/i+Ylz5zIcQWBtcGxH3RAT8SjRJ6sLA5PCLaeQaLQvEOqZ++1ImSP9BhFyouQtV7zHWjWNUep8tqykDHdCXNLkvQy7BFnxaVvRVMO0sLRlHnB+NNOVEk/XCOili17f8Yqa+fdtpROXn3hkd4iWJAKgXYPnfuFiilj4lJz4uw+ePB3u7EIR4XQjU96SM2xmSaV3lzAMtlZDrIadskEpSK9bdkJC/8ftJmdFEAmOd+fAegVofvr/tdyWKCbsHN9DJXgvNTKl/GOLxfGWQ=="
        "/response/fr-wrong-certificate.json"   || "MIIC8DCCAdgCCQDfzHxquGqBiTANBgkqhkiG9w0BAQsFADA6MQswCQYDVQQGEwJERTEQMA4GA1UEBwwHR2VybWFueTEZMBcGA1UEAwwQc2VydmljZWxheWVycy5pbzAeFw0yMjAyMTUxNDIxNTBaFw0yNzAyMTQxNDIxNTBaMDoxCzAJBgNVBAYTAkRFMRAwDgYDVQQHDAdHZXJtYW55MRkwFwYDVQQDDBBzZXJ2aWNlbGF5ZXJzLmlvMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAphWxfZscdXfKa7q5ZQoqWNvXj0NuUmPT17EDUtTywI/w14H4mwwcFBhaFcPTKx/8TmWDXsawj8wxRr3zL9AVRkp5vSuexLdhIfyLiq+uupwZDUfSptiDTZiCr7V0FwzusoNyQD8a0EDpkM1zJSqCNr7SBTAButx5ZD/r8xEIYi4xcL2UZemgoBNEsY8e/R1R5kt5aTn2+QmgeqQriJovjofdDl7M2taZjn6QXNtLT4JRCtk+uLIzbyNZXREDvsO6c5f2F/rQNrovYt1hg3BLrb7liehGc5xUg9xEvFADxFEHPtZalqxUTX5wluLj/fZyZtdXqdo4RS4lNxCRAfvgFQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQAUfXId9hRu2C60oPTKUlQysyHSxGhzRepmpkW4FhlZEng3FiVQaWKHodR45Z2ef+i2NI+bF3C5ptWTj7pdtusZm8hqTRU5++2nN0xg5zhHTldz/ugK1qzmopnv9ZCqdzJhP6OWPVIJWl+GftIM3qfp768i8MFUh57EpSw2CkLI3bt0rF5KCLfAhhj9zhBdR0afOIQeb1nGdbXZeGzZjCUy3rzRqNRC8dki9KBAfkvrtVIzuRaCr8pSCG2DTY1TzVqQIurnQ2D5r9PVkBnbZuS5Witul+fEokfJS86Aq8lR+kqWfrSFL30v2McQQBFzaT26Vsmk0g/PYyx+lr67rMaa"
        "/response/fr-invalid-certificate.json" || "12345"
        ""                                      || ""
    }

    def "Get Certificate from Memcached"() {
        given: "object mapper read mock response from /jwk_uri endpoint"
        def frJwtKeyResponse = (!"".equalsIgnoreCase(keyFileName)) ? mapper.readValue(this.getClass().getResource(keyFileName).text, FrJwtKeyResponse) : ""

        and: "we have a mock request prepared for us"
        def httpRequestPreparer = Mock(HttpRequestPreparer)

        and: "forgerock client"
        def httpClient = Mock(HttpClient)
        def frApiClient = new FrApiClient(httpRequestPreparer, httpClient)

        and: "mock the response for the request"
        def mockResponse = Mock(HttpResponse)
        mockResponse.body() >> new ObjectMapper().writeValueAsString(frJwtKeyResponse)
        httpClient.send(_, _) >> mockResponse

        and: "memcached client"
        def memcachedClient = Mock(MemcachedClient)
        def cachedAwsClient = Mock(CachedAwsClient)
        cachedAwsClient.getClient() >> memcachedClient

        and: "mock certificate from mock response"
        def certificate =  validFrCertificate(frJwtKeyResponse)
        cachedAwsClient.getKey(_, _) >> certificate

        when: "get certificate method is called"
        def certificateService = new CertificateService(frApiClient, cachedAwsClient)
        def output = certificateService.getCertificate()

        then: "response says whether token is active"
        output == expectedCertificate

        where:
        keyFileName                             || expectedCertificate
        "/response/fr-right-certificate.json"   || "12345@certSeperatorMIIDPzCCAicCFDaux5Nqp7A3u9lAggNdBGiRjJgKMA0GCSqGSIb3DQEBCwUAMFwxCzAJBgNVBAYTAkdCMRMwEQYDVQQIDApNYW5jaGVzdGVyMQwwCgYDVQQKDANKTFIxKjAoBgkqhkiG9w0BCQEWG2ZjaGVuZzFAamFndWFybGFuZHJvdmVyLmNvbTAeFw0yMzAyMjExMTQwMzRaFw0yNDAyMjExMTQwMzRaMFwxCzAJBgNVBAYTAkdCMRMwEQYDVQQIDApNYW5jaGVzdGVyMQwwCgYDVQQKDANKTFIxKjAoBgkqhkiG9w0BCQEWG2ZjaGVuZzFAamFndWFybGFuZHJvdmVyLmNvbTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAMenrDCTh5O5FO8aLZxyW0zaflUttzdM/ji0sTpNBbiYnFFvC+Dw+oeIAEHV213AsgvwNgF5EZ/MGFANSFDBWKejNuHOhcATI2nUSKpKQCgI6h9glwYJSxTuDfKhRwdfs9szmfMxed5qdi1B9pc3Mdkc4MwLd7COv93QKn7pZ/kCi0/A5xaEKHjSd7lGEF0a9sfqQ3xVyZYg+8Nlf/wfPn0qZ1+WrG+VEVuU87siawk2NOlVbACa0ycZf0HB7Fr2zz0b0fO8drVsjqJIzbH5KDa6PvFuOg9LWfJzUtLl5AARCVhLDnTX+qXRcX8oYYQ2QTbLL6CDc4be8SSLYNIHeGcCAwEAATANBgkqhkiG9w0BAQsFAAOCAQEAUNff3yk/5PBdScIVsUbqI2oSCluGrsSXUmC5KST+rXN+NE38p3a5RUTTQ+Srl57my5RspdP/i+Ylz5zIcQWBtcGxH3RAT8SjRJ6sLA5PCLaeQaLQvEOqZ++1ImSP9BhFyouQtV7zHWjWNUep8tqykDHdCXNLkvQy7BFnxaVvRVMO0sLRlHnB+NNOVEk/XCOili17f8Yqa+fdtpROXn3hkd4iWJAKgXYPnfuFiilj4lJz4uw+ePB3u7EIR4XQjU96SM2xmSaV3lzAMtlZDrIadskEpSK9bdkJC/8ftJmdFEAmOd+fAegVofvr/tdyWKCbsHN9DJXgvNTKl/GOLxfGWQ=="
        "/response/fr-wrong-certificate.json"   || "MIIC8DCCAdgCCQDfzHxquGqBiTANBgkqhkiG9w0BAQsFADA6MQswCQYDVQQGEwJERTEQMA4GA1UEBwwHR2VybWFueTEZMBcGA1UEAwwQc2VydmljZWxheWVycy5pbzAeFw0yMjAyMTUxNDIxNTBaFw0yNzAyMTQxNDIxNTBaMDoxCzAJBgNVBAYTAkRFMRAwDgYDVQQHDAdHZXJtYW55MRkwFwYDVQQDDBBzZXJ2aWNlbGF5ZXJzLmlvMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAphWxfZscdXfKa7q5ZQoqWNvXj0NuUmPT17EDUtTywI/w14H4mwwcFBhaFcPTKx/8TmWDXsawj8wxRr3zL9AVRkp5vSuexLdhIfyLiq+uupwZDUfSptiDTZiCr7V0FwzusoNyQD8a0EDpkM1zJSqCNr7SBTAButx5ZD/r8xEIYi4xcL2UZemgoBNEsY8e/R1R5kt5aTn2+QmgeqQriJovjofdDl7M2taZjn6QXNtLT4JRCtk+uLIzbyNZXREDvsO6c5f2F/rQNrovYt1hg3BLrb7liehGc5xUg9xEvFADxFEHPtZalqxUTX5wluLj/fZyZtdXqdo4RS4lNxCRAfvgFQIDAQABMA0GCSqGSIb3DQEBCwUAA4IBAQAUfXId9hRu2C60oPTKUlQysyHSxGhzRepmpkW4FhlZEng3FiVQaWKHodR45Z2ef+i2NI+bF3C5ptWTj7pdtusZm8hqTRU5++2nN0xg5zhHTldz/ugK1qzmopnv9ZCqdzJhP6OWPVIJWl+GftIM3qfp768i8MFUh57EpSw2CkLI3bt0rF5KCLfAhhj9zhBdR0afOIQeb1nGdbXZeGzZjCUy3rzRqNRC8dki9KBAfkvrtVIzuRaCr8pSCG2DTY1TzVqQIurnQ2D5r9PVkBnbZuS5Witul+fEokfJS86Aq8lR+kqWfrSFL30v2McQQBFzaT26Vsmk0g/PYyx+lr67rMaa"
        "/response/fr-invalid-certificate.json" || "12345"
        ""                                      || ""
    }

    def "Certificate Service logs caught exceptions"() {
        given: "object mapper read mock response from /jwk_uri endpoint"
        def frJwtKeyResponse = (!"".equalsIgnoreCase("/response/fr-right-certificate.json")) ? mapper.readValue(this.getClass().getResource("/response/fr-right-certificate.json").text, FrJwtKeyResponse) : ""

        and: "we have a mock request prepared for us"
        def httpRequestPreparer = Mock(HttpRequestPreparer)
        def mockRequest = Mock(HttpRequest)
        httpRequestPreparer.create() >> mockRequest

        and: "forgerock client"
        def httpClient = Mock(HttpClient)
        def frApiClient = new FrApiClient(httpRequestPreparer, httpClient)

        and: "memcached client"
        def memcachedClient = Mock(MemcachedClient)
        def cachedAwsClient = Mock(CachedAwsClient)
        cachedAwsClient.getClient() >> Mock(MemcachedClient)

        and: "mock the response for the request"
        def mockResponse = Mock(HttpResponse)
        mockResponse.body() >> new ObjectMapper().writeValueAsString(frJwtKeyResponse)
        httpClient.send(_, _) >> mockResponse

        and: "certificate service"
        def certificateService = new CertificateService(frApiClient, cachedAwsClient)
        cachedAwsClient.getClient() >> memcachedClient

        when: "getCertificate is called"
        def certificate = certificateService.getCertificate()

        then: "certificate is not null"
        certificate != null

        and: "cachedAwsClient.getKey is called once"
        1 * cachedAwsClient.getKey(_, _) >> { throw exception }

        and: "Thread was interrupted if InterruptedException"
        if (exception instanceof InterruptedException)
            assert Thread.currentThread().isInterrupted()

        where:
        exception                  | expectedLogMessage
        Mock(ExecutionException)   | "Failed to get certificate from Memcached."
        Mock(TimeoutException)     | "Failed to get certificate from Memcached."
        Mock(InterruptedException) | "Got InterruptedException."
    }

    def validFrCertificate(frJwtResponse) {
        def certificate = ""
        if (frJwtResponse instanceof FrJwtKeyResponse) {
            FrJwtKeyResponse response = (FrJwtKeyResponse) frJwtResponse
            StringBuilder stringBuilder = new StringBuilder()
            List<FrJwtKey> keys = response.getKeys()
            for (int i = 0, size = keys.size(); i < size; i++) {
                stringBuilder.append(keys.get(i).getX5c().get(0))
                if (i<size-1){
                    stringBuilder.append(CERT_SEPERATOR)
                }
            }
            certificate =  stringBuilder.toString()
        }
        certificate
    }
}