/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.helper

import io.jsonwebtoken.Jwts
import io.jsonwebtoken.SignatureAlgorithm

import javax.crypto.SecretKey
import javax.crypto.SecretKeyFactory
import javax.crypto.spec.PBEKeySpec
import javax.crypto.spec.SecretKeySpec
import java.security.NoSuchAlgorithmException
import java.security.spec.InvalidKeySpecException
import java.security.spec.KeySpec

class ApproovTokenTestHelper {

    // FR JWT settings
    static final int EXPIRATION_IN_SECONDS = 120000
    private static final Date issueDate = new Date()

    static def buildApproovValidToken() {
        return Jwts.builder()
                .setIssuedAt(issueDate)
                .setExpiration(new Date(issueDate.getTime() + EXPIRATION_IN_SECONDS))
                .signWith(approovSecretKey, SignatureAlgorithm.HS256)
                .compact()
    }

    static def buildApproovInvalidToken() {
        return Jwts.builder()
                .setIssuedAt(issueDate)
                .setExpiration(new Date(issueDate.getTime() - EXPIRATION_IN_SECONDS))
                .signWith(approovSecretKey, SignatureAlgorithm.HS256)
                .compact()
    }

    private static def getApproovSecretKey() throws NoSuchAlgorithmException, InvalidKeySpecException {
        byte[] salt = new byte[100]
        SecretKeyFactory factory = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA256")
        KeySpec spec = new PBEKeySpec("password".toCharArray(), salt, 65536, 256)
        SecretKey tmp = factory.generateSecret(spec)
        SecretKey secretKey = new SecretKeySpec(tmp.getEncoded(), "HmacSHA256")

        return secretKey
    }

    static def getEncodedApproovSecretKeyString() throws NoSuchAlgorithmException, InvalidKeySpecException {

        return approovSecretKey.encoded.encodeBase64().toString()
    }
}
