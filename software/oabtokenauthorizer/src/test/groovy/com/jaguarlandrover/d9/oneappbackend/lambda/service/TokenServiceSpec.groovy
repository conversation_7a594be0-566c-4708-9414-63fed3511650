/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.service

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent
import spock.lang.Specification

class TokenServiceSpec extends Specification {

    def ENCODED_FORGEROCK_TOKEN = "some_string_value"
    TokenService tokenService = new TokenService()

    def "extracts auth token from APIGatewayProxyRequestEvent headers"() {

        given: "event"
        def event = new APIGatewayProxyRequestEvent()

        and: "headers containing authorization"
        def headers = [(authHeaderKey): ENCODED_FORGEROCK_TOKEN]

        and: "event contains headers"
        event.setHeaders(headers)

        when: "event is sent to the service"
        def output = tokenService.getFrAuthorizationToken(event)

        then: "output contains auth token"
        output.get() == ENCODED_FORGEROCK_TOKEN

        where:
        authHeaderKey   | _
        "authorization" | _
        "Authorization" | _
        "AUTHORIZATION" | _
    }

    def "returns empty optional if no auth header"() {

        given: "event"
        def event = new APIGatewayProxyRequestEvent()

        and: "headers not containing authorization"
        def headers = [(authHeaderKey): ENCODED_FORGEROCK_TOKEN]

        and: "event contains headers"
        event.setHeaders(headers)

        when: "event is sent to the service"
        def output = tokenService.getFrAuthorizationToken(event)

        then: "output contains auth token"
        output == Optional.empty()

        where:
        authHeaderKey           | _
        null                    | _
        ""                      | _
        "Authorisation"         | _
        "any other wrong value" | _
    }

    def "returns empty optional if no headers"() {

        given: "event"
        def event = new APIGatewayProxyRequestEvent()

        when: "event is sent to the FR service"
        def outputForFRToken = tokenService.getFrAuthorizationToken(event)

        and: "event is sent to the Approov service"
        def outputForApproovToken = tokenService.getApproovAuthorizationToken(event)

        then: "FR token and Approov token are Optional.empty"
        outputForFRToken == Optional.empty()

        and: ""
        outputForApproovToken == Optional.empty()
    }

}