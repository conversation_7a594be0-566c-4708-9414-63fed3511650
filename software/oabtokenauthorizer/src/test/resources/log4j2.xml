<?xml version="1.0" encoding="UTF-8"?>
<Configuration packages="com.amazonaws.services.lambda.runtime.log4j2" status="trace">
    <Appenders>
        <Lambda name="Lambda" format="JSON">
            <LambdaTextFormat>
                <PatternLayout>
                    <pattern>%d{yyyy-MM-dd HH:mm:ss} %X{AWSRequestId} %-5p %c{1}:%L - %m%n</pattern>
                </PatternLayout>
            </LambdaTextFormat>
            <LambdaJSONFormat>
                <JsonTemplateLayout eventTemplateUri="classpath:LambdaLayout.json" />
            </LambdaJSONFormat>
        </Lambda>
        <File name="JsonLogfileAppender" fileName="${sys:logFilename}" append="false">
            <JSONLayout compact="true" eventEol="true">
                <KeyValuePair key="http.request_id" value="${ctx:http.request_id}"/>
            </JSONLayout>
            <BurstFilter level="INFO" rate="2" maxBurst="10"/>
        </File>
    </Appenders>
    <Loggers>
        <Root name="CoreLogger" level="INFO">
            <AppenderRef ref="Lambda" />
        </Root>
        <Logger name="software.amazon.awssdk" level="WARN" />
        <Logger name="software.amazon.awssdk.request" level="DEBUG" />
    </Loggers>
</Configuration>