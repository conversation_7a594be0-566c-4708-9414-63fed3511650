/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.service;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TokenService {

  /**
   * Get the value of specific header.  If not found, return empty string.
   * @param event API Gateway Request Event
   * @param header name of the header
   * @return value of the specific header
   */
  public Optional<String> getRequestHeaderValue(APIGatewayProxyRequestEvent event, String header) {
    if (event.getHeaders() == null || event.getHeaders().entrySet() == null) {
      log.info("########## header is null");
      return Optional.empty();
    }

    Optional<String> authHeaderOptional = event.getHeaders().entrySet().stream()
        .filter(entry -> header.equalsIgnoreCase(entry.getKey()))
        .map(Map.Entry::getValue)
        .findAny();
    if (authHeaderOptional.isEmpty()) {
      log.info("########## Start logging headers");
      for (Map.Entry<String, String> entry : event.getHeaders().entrySet()) {
        log.info("Key : {}", entry.getKey());
      }
      log.info("########## end logging headers");
      log.info("#### received request with no header key: {}", header);
      return Optional.empty();
    }

    return authHeaderOptional;
  }

  public Optional<String> getFrAuthorizationToken(APIGatewayProxyRequestEvent event) {
    return getRequestHeaderValue(event, "authorization");
  }

  public Optional<String> getApproovAuthorizationToken(APIGatewayProxyRequestEvent event) {
    return getRequestHeaderValue(event, "attestation");
  }


}
