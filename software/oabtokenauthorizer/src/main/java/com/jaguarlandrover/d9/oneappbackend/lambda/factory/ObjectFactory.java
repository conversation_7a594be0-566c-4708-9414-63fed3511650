/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.factory;

import com.jaguarlandrover.d9.oneappbackend.lambda.client.CachedAwsClient;
import com.jaguarlandrover.d9.oneappbackend.lambda.client.FrApiClient;
import com.jaguarlandrover.d9.oneappbackend.lambda.client.OabMemcachedClient;
import com.jaguarlandrover.d9.oneappbackend.lambda.helper.EnvironmentVariables;
import com.jaguarlandrover.d9.oneappbackend.lambda.helper.SecretsManagerHelper;
import com.jaguarlandrover.d9.oneappbackend.lambda.util.HttpRequestPreparer;
import com.jaguarlandrover.d9.oneappbackend.lambda.util.PolicyDocumentCreator;
import java.io.IOException;
import java.net.http.HttpClient;


// This class behaves like Spring Framework
public class ObjectFactory {

  public static final String FR_JWK_URI =
      "%s/gateway/oauth2/realms/root/realms/customer/connect/jwk_uri";

  // Change for Adding AWS Cache Implementation
  public static final int CACHE_READ_TIME_OUT = 2000;
  public static final int CACHE_EXPIRY_TIME = 3600;
  public static final String CACHE_KEY = "FR_PK_KEY";

  private final EnvironmentVariables environmentVariables;
  private final OabMemcachedClient oabMemcachedClient;
  private final SecretsManagerHelper secretsManagerHelper;

  /**
   * Constructor.
   */
  public ObjectFactory(EnvironmentVariables environmentVariables,
                       OabMemcachedClient oabMemcachedClient,
                       SecretsManagerHelper secretsManagerHelper) {
    this.environmentVariables = environmentVariables;
    this.oabMemcachedClient = oabMemcachedClient;
    this.secretsManagerHelper = secretsManagerHelper;
  }

  public FrApiClient frApiClient() {
    return new FrApiClient(httpRequestPreparer(), HttpClient.newHttpClient());
  }

  public PolicyDocumentCreator policyDocumentCreator() {
    return new PolicyDocumentCreator(getPolicyArnFormat());
  }

  public HttpRequestPreparer httpRequestPreparer() {
    return new HttpRequestPreparer(frJwkUri());
  }

  public String frJwkUri() {
    return String.format(FR_JWK_URI, environmentVariables.getVariable("FORGEROCK_HOST"));
  }

  public CachedAwsClient cachedAwsClient() throws IOException {
    return new CachedAwsClient(getCacheEndpoint(), getCachePort(), oabMemcachedClient);
  }

  public EnvironmentVariables environmentVariables() {
    return environmentVariables;
  }

  public String getCacheEndpoint() {
    return environmentVariables.getVariable("CACHE_ENDPOINT");
  }

  public int getCachePort() {
    return Integer.parseInt(environmentVariables.getVariable("CACHE_PORT"));
  }

  public SecretsManagerHelper secretsManagerHelper() {
    return secretsManagerHelper;
  }

  public String getPolicyArnFormat() {
    return environmentVariables.getVariable("POLICY_ARN_FORMAT");
  }
}
