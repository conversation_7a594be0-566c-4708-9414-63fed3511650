/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class JwtPayload {

  private String aud;

  private String sub;

  @JsonProperty("mgd-attributes")
  private List<String> mgdAttributes;

  @JsonProperty("mgd-identifiers")
  private List<String> mgdIdentifiers;

  private Long exp;

}
