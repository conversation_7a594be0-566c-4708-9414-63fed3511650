/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.helper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jaguarlandrover.d9.oneappbackend.lambda.dto.JwtPayload;
import java.util.Base64;
import java.util.Calendar;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;


/**
 * To parse a ForgeRock JWT Access Token.
 */
@Slf4j
public class TokenParser {

  /**
   * Used to obtain audience from JWT.
   */
  public static final String OAB_FR_AUDIENCE = "oneapp-rangerover";

  /**
   * Key of Principal ID in the context attributes map to be returned to AWS API Gateway in the Lambda Authorizer.
   */
  public static final String PRINCIPAL_ID_KEY = "principalId";
  private static final String PRINCIPAL_ID_URN_PATTERN = "urn:iam2-mgd-v1:mgd-identifiers:oneapp-rangerover:auto-id:principal-uuid:(.*)";

  /**
   * Key of Auto ID in the attributes map to be returned to AWS API Gateway in the Lambda Authorizer..
   */
  public static final String AUTO_ID_KEY = "autoId";
  private static final String AUTO_ID_URN
      = "urn:iam2-mgd-v1:mgd-identifiers:customer:auto-id:principal-uuid";
  private static final String AUTO_ID_URN_PATTERN = AUTO_ID_URN + ":(.*)";
  private static final Pattern autoIdPattern
      = Pattern.compile(AUTO_ID_URN_PATTERN, Pattern.CASE_INSENSITIVE);

  /**
   * Key of SVCRM IDs in the attributes map to be returned to AWS API Gateway in the Lambda Authorizer..
   */
  public static final String SVCRM_ID_KEY = "svcrmId";
  private static final String SVCRM_ID_URN
      = "urn:iam2-mgd-v1:mgd-identifiers:customer:person:iam2-consumer-rights-v1:SVCRM:crmId";
  private static final String SVCRM_ID_URN_PATTERN = SVCRM_ID_URN + ":(.*):(.*)";
  private static final Pattern svcrmIdPattern
      = Pattern.compile(SVCRM_ID_URN_PATTERN, Pattern.CASE_INSENSITIVE);

  /**
   * Key of Vehicle IDs in the context attributes map to be returned to AWS API Gateway in the Lambda Authorizer.
   */
  public static final String VEHICLE_ID_KEY = "vehicleIds";
  public static final String DV_VEHICLE_ID_URN
      = "urn:iam2-mgd-v1:mgd-attributes:vehicle:vehicle-identity:iam2-consumer-rights-v1:digital-vehicle:vehicle-id";
  public static final String DV_VEHICLE_ID_URN_PATTERN = DV_VEHICLE_ID_URN + ":(.*):(.*)";
  private static final Pattern dvVehicleIdPattern
      = Pattern.compile(DV_VEHICLE_ID_URN_PATTERN, Pattern.CASE_INSENSITIVE);

  private final ObjectMapper objectMapper;

  public TokenParser() {
    this.objectMapper = new ObjectMapper();
  }

  /**
   * Parse the given ForgeRock JWT Access Token to return a map of attributes used by our API Gateway.
   * @param token ForgeRock JWT Access Token
   * @return a map of attributes
   */
  public Map<String, String> retrieveTokenAttributes(String token) {
    Map<String, String> attributes = new HashMap<>();

    // Extract the payload
    final String[] chunks = token.split("\\.");
    final Base64.Decoder decoder = Base64.getUrlDecoder();
    if (chunks.length != 3) {
      // Invalid token format
      return attributes;
    }
    final String payload = new String(decoder.decode(chunks[1]));

    try {
      JwtPayload jwtPayload = objectMapper.readValue(payload, JwtPayload.class);

      if (validateToken(jwtPayload)) {

        // Principal ID
        Pattern pattern = Pattern.compile(PRINCIPAL_ID_URN_PATTERN, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(jwtPayload.getSub());
        if (matcher.find()) {
          attributes.put(PRINCIPAL_ID_KEY, matcher.group(1));
        }

        // Auto ID
        if (jwtPayload.getMgdIdentifiers() != null) {
          String autoIds = jwtPayload.getMgdIdentifiers().stream()
              .filter(a -> a != null && a.contains(AUTO_ID_URN))
              .map(this::findAutoId)
              .collect(Collectors.joining(","));
          attributes.put(AUTO_ID_KEY, autoIds);
        }

        // SVCRM ID
        if (jwtPayload.getMgdIdentifiers() != null) {
          String svcrmIds = jwtPayload.getMgdIdentifiers().stream()
              .filter(a -> a != null && a.contains(SVCRM_ID_URN))
              .map(this::findSvcrmId)
              .collect(Collectors.joining(","));
          attributes.put(SVCRM_ID_KEY, svcrmIds);
        }

        // Digital Vehicle GUIDs
        if (jwtPayload.getMgdAttributes() != null) {
          String guids = jwtPayload.getMgdAttributes().stream()
              .filter(a -> a != null && a.contains(DV_VEHICLE_ID_URN))
              .map(this::findVehicleId)
              .collect(Collectors.joining(","));
          attributes.put(VEHICLE_ID_KEY, guids);
        }
      }
    } catch (JsonProcessingException e) {
      log.error("Failed to read payload from the JWT token.", e);
    }

    return attributes;
  }

  /**
   * Validate the given JWT token.
   * 1. Check token expiry: only set the attributes if the token is not yet expired.
   * 2. Check Sub: only read the managed attributes and managed identifiers when sub is not null
   * 3. Check the audience
   * @param jwtPayload payload of the JWT token
   * @return true if the token is valid. false if otherwise.
   */
  private boolean validateToken(JwtPayload jwtPayload) {
    if ((jwtPayload.getExp() != null
        && jwtPayload.getExp() >= Calendar.getInstance().getTimeInMillis() / 1000)) {
      return jwtPayload.getSub() != null
        && jwtPayload.getAud() != null
        && jwtPayload.getAud().equalsIgnoreCase(OAB_FR_AUDIENCE);
    } else {
      return false;
    }
  }

  /**
   * Grep the Auto ID from the given URN.
   * @param urn the URN to be grep
   * @return the Auto ID
   */
  private String findAutoId(String urn) {
    Matcher matcher = autoIdPattern.matcher(urn);
    if (matcher.find()) {
      return matcher.group(1);
    }
    return null;
  }

  /**
   * Grep the SVCRM ID from the given URN.
   * @param urn the URN to be grep
   * @return the SVCRM ID
   */
  private String findSvcrmId(String urn) {
    Matcher matcher = svcrmIdPattern.matcher(urn);
    if (matcher.find()) {
      return matcher.group(1);
    }
    return null;
  }

  /**
   * Grep the vehicle ID from the given URN.
   * @param urn the URN to be grep
   * @return the vehicle ID
   */
  private String findVehicleId(String urn) {
    Matcher matcher = dvVehicleIdPattern.matcher(urn);
    if (matcher.find()) {
      return matcher.group(1);
    }
    return null;
  }

}
