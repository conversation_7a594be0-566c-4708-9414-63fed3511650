/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.service;

import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import java.util.Base64;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class ApproovAuthorizationService {

  /**
   * Validate the Approov access token.
   * @param approovToken Approov JWT Access Token
   * @param secretKey Approov certificate
   * @return true if the token is valid. false if otherwise.
   */
  public boolean isApproovTokenValid(String approovToken, String secretKey) {
    boolean result = false;
    try {
      Jwts.parserBuilder()
          .setSigningKey(Base64.getDecoder().decode(secretKey))
          .build()
          .parseClaimsJws(approovToken)
          .getBody();
      result = true;
    } catch (JwtException e) {
      log.error("Failed to validate the Approov token.", e);
    }

    return result;

  }
}
