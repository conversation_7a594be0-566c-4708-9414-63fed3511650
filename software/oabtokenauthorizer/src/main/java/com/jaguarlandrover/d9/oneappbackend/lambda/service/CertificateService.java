/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.jaguarlandrover.d9.oneappbackend.lambda.client.CachedAwsClient;
import com.jaguarlandrover.d9.oneappbackend.lambda.client.FrApiClient;
import com.jaguarlandrover.d9.oneappbackend.lambda.dto.FrJwtKey;
import com.jaguarlandrover.d9.oneappbackend.lambda.dto.FrJwtKeyResponse;
import com.jaguarlandrover.d9.oneappbackend.lambda.factory.ObjectFactory;
import java.net.http.HttpResponse;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;
import lombok.extern.slf4j.Slf4j;
import net.spy.memcached.MemcachedClient;


@Slf4j
public class CertificateService {

  public static final String CERT_SEPERATOR = "@certSeperator";

  private final FrApiClient frApiClient;
  private final CachedAwsClient cachedAwsClient;

  public CertificateService(FrApiClient frApiClient, CachedAwsClient cachedAwsClient) {
    this.frApiClient = frApiClient;
    this.cachedAwsClient = cachedAwsClient;
  }

  /**
   * Get certificate from Memcached. If not exists, get it from ForgeRock server.
   * @return ForgeRock certificate at /jwk_uri.
   */
  public String getCertificate() {

    // Get certificate from Memcached.
    MemcachedClient cacheClient = cachedAwsClient.getClient();
    log.info("cacheClient: {}", cacheClient);
    if (cacheClient != null) {
      log.info("Trying to get certificate from Memcached");
      try {
        String certificate =
            cachedAwsClient.getKey(ObjectFactory.CACHE_KEY, ObjectFactory.CACHE_READ_TIME_OUT);
        if (certificate != null) {
          log.info("Got the certificate from Memcached.");
          return certificate;
        }
      } catch (ExecutionException | TimeoutException e) {
        log.error("Failed to get certificate from Memcached.", e);
      } catch (InterruptedException e) {
        log.error("Got InterruptedException.", e);
        Thread.currentThread().interrupt();
      }
    }

    // No certificate found from Memcached. Get the certificate from ForgeRock server
    log.info("No certificate found from Memcached. Getting certificate from ForgeRock.");
    HttpResponse<String> tokenResponse = frApiClient.getFrTokenResponse();
    String certificate = extractCertFromFrResponse(tokenResponse);

    // Store the key into Memcached.
    cachedAwsClient.setKey(ObjectFactory.CACHE_KEY, certificate, ObjectFactory.CACHE_EXPIRY_TIME);

    return certificate;
  }

  /**
   * Extract the public certificate from the /jwk_uri response.
   * @param response response from ForgeRock /jwk_uri endpoint
   * @return the public certificate. Empty string in case of error.
   */
  private String extractCertFromFrResponse(HttpResponse<String> response) {
    try {
      ObjectMapper mapper = new ObjectMapper();
      FrJwtKeyResponse frResponse = mapper.readValue(response.body(), FrJwtKeyResponse.class);
      StringBuilder stringBuilder = new StringBuilder();
      List<FrJwtKey> keys = frResponse.getKeys();
      for (int i = 0, size = keys.size(); i < size; i++) {
        stringBuilder.append(keys.get(i).getX5c().get(0));
        if (i < size - 1) {
          stringBuilder.append(CERT_SEPERATOR);
        }
      }
      return stringBuilder.toString();
    } catch (JsonProcessingException e) {
      log.error("Failed to extract the public certificate from the response.", e);
      return "";
    }
  }

}
