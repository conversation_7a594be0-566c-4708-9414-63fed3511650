/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.service;

import static com.jaguarlandrover.d9.oneappbackend.lambda.helper.TokenParser.OAB_FR_AUDIENCE;

import com.jaguarlandrover.d9.oneappbackend.lambda.helper.EnvironmentVariables;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.IncorrectClaimException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.MissingClaimException;
import io.jsonwebtoken.UnsupportedJwtException;
import io.jsonwebtoken.io.DecodingException;
import io.jsonwebtoken.lang.Strings;
import io.jsonwebtoken.security.SignatureException;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.security.PublicKey;
import java.security.cert.Certificate;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.util.Base64;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class FrAuthorizationService {

  /**
   * Validate the access token.
   * @param token ForgeRock JWT Access Token
   * @param cert ForgeRock certificate
   * @return true if the token is valid. false if otherwise.
   */
  public boolean isTokenValid(String token, String cert,
                              final EnvironmentVariables environmentVariables) {
    try {
      PublicKey key = getPublicKeyByCert(cert);

      // Token must first be decoded, with an exception leading to an unauthorized request
      Claims claims = Jwts.parserBuilder()
          .setSigningKey(key)
          .requireAudience(OAB_FR_AUDIENCE)
          .build()
          .parseClaimsJws(token)
          .getBody();

      if (claims.getSubject() == null) {
        return false;
      }

      if (!isIssuerClaimValid((String) claims.get("iss"), environmentVariables.getVariable("FORGEROCK_HOST"))) {
        return false;
      }

      return true;
    } catch (CertificateException e) {
      log.error("Failed to get public key from certificate.", e);
    } catch (DecodingException e) {
      log.error("Failed to decode the token.", e);
    } catch (IllegalArgumentException
      | ExpiredJwtException | MalformedJwtException | UnsupportedJwtException
      | SignatureException | MissingClaimException
      | IncorrectClaimException e) {
      log.error("Failed to validate the token.", e);
    }

    return false;
  }

  /**
   * Validate the Issuer claim.
   * @param iss issuer URL
   * @param forgerockHost Forgerock host url
   * @return true if the issuer claim is valid. false if otherwise.
   */
  private boolean isIssuerClaimValid(final String iss, final String forgerockHost) {
    return Strings.hasLength(iss) && iss.contains(forgerockHost);
  }

  private PublicKey getPublicKeyByCert(String certb64) throws CertificateException {
    byte[] certder = Base64.getDecoder().decode(certb64);
    InputStream certstream = new ByteArrayInputStream(certder);
    Certificate cert = CertificateFactory.getInstance("X.509").generateCertificate(certstream);

    return cert.getPublicKey();
  }

}
