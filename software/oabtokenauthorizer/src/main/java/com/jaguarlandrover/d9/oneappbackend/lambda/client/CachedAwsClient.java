/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.client;

import com.jaguarlandrover.d9.oneappbackend.lambda.util.LogUtility;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import net.spy.memcached.MemcachedClient;
import net.spy.memcached.internal.GetFuture;
import org.slf4j.Logger;


public class CachedAwsClient {

  private static final Logger LOGGER = LogUtility.getInstance();

  private OabMemcachedClient oabMemcachedClient;

  /**
   * Constructor.
   * @param endpoint endpoint of the Memcached
   * @param port port of the Memcached
   */
  public CachedAwsClient(String endpoint, int port, OabMemcachedClient oabMemcachedClient) throws IOException {
    this.oabMemcachedClient = oabMemcachedClient;
    oabMemcachedClient.initializeMemcachedClient(endpoint, port);
  }

  /**
   * Get MemCached client.
   * @return MemCached client
   */
  public MemcachedClient getClient() {
    return oabMemcachedClient.getClient();
  }


  /**
   * Get key from MemCache.
   * @param key key of the cache
   * @param readTimeout timeout for getting the cache
   * @return storedValue value of the cache
   */
  public String getKey(String key, int readTimeout)
      throws ExecutionException, InterruptedException, TimeoutException {
    GetFuture<Object> future = this.oabMemcachedClient.asyncGet(key);

    Object object = future.get(readTimeout, TimeUnit.SECONDS);

    if (object != null) {
      LOGGER.info(
          "[CachedAwsClient] Getting the certificate from Memcached ### key: {}, readtimeout: {}", key, readTimeout);
      return object.toString();
    }
    return null;
  }

  /**
   * Set key in MemCached.
   * @param key key of the cache
   * @param value value of the cache
   * @param expiryTime expiry time in seconds
   */
  public void setKey(String key, String value, int expiryTime) {
    LOGGER.info(
        "[CachedAwsClient] Saving the certificate in Memcached ### key: {}, expiryTime: {}",
        key, expiryTime);
    oabMemcachedClient.set(key, expiryTime, value);
  }

}
