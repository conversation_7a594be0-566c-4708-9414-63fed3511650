/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.util;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class HttpRequestPreparer {

  public final String frUrl;

  public HttpRequestPreparer(String frUrl) {
    this.frUrl = frUrl;
    log.info("url: {}", frUrl);
  }

  /**
   * Create HttpRequest.
   */
  public HttpRequest create() {
    HttpRequest request = null;

    // build the HttpRequest
    try {
      request = HttpRequest.newBuilder()
          .uri(new URI(frUrl))
          .GET()
          .build();
    } catch (URISyntaxException e) {
      log.info("wrong syntax");
      return null;
    }

    log.info("Finishing create HttpRequest");
    return request;
  }

}
