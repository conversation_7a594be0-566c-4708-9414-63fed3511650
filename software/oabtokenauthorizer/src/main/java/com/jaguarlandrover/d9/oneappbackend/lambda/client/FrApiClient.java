/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.client;

import com.jaguarlandrover.d9.oneappbackend.lambda.util.HttpRequestPreparer;
import com.jaguarlandrover.d9.oneappbackend.lambda.util.LogUtility;
import java.io.IOException;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import org.slf4j.Logger;


public class FrApiClient {

  private static final Logger LOGGER = LogUtility.getInstance();

  private final HttpRequestPreparer httpRequestPreparer;
  private final HttpClient httpClient;

  public FrApiClient(HttpRequestPreparer httpRequestPreparer, HttpClient httpClient) {
    this.httpRequestPreparer = httpRequestPreparer;
    this.httpClient = httpClient;
  }

  // remove logic from client, give response - feign client maybe?
  /**
   * Get the public keys from ForgeRock server.
   * @return string containing the public keys
   */
  public HttpResponse<String> getFrTokenResponse() {
    LOGGER.info("[FrApiClient] Start connecting FR & get response ...");
    HttpResponse<String> response = null;
    try {
      HttpRequest request = httpRequestPreparer.create();

      if (request == null) {
        return null;
      }

      LOGGER.info("[FrApiClient] Prepared request for /jwk_uri endpoint: {}", request);

      response = httpClient.send(request,
          HttpResponse.BodyHandlers.ofString());
      LOGGER.info("[FrApiClient] Received response from /jwk_uri endpoint: {}", response);

      if (response != null) {
        LOGGER.info("[FrApiClient] Status Code: {}", response.statusCode());
      }

    } catch (InterruptedException ie) {
      Thread.currentThread().interrupt();
      LOGGER.info("[FrApiClient] Received InterruptedException: ", ie);
      LOGGER.error("[FrApiClient] " + ie.getMessage());
    } catch (IOException e) {
      LOGGER.info("[FrApiClient] Received IOException: ", e);
      LOGGER.error("[FrApiClient] " + e.getMessage());
    }

    return response;
  }

}
