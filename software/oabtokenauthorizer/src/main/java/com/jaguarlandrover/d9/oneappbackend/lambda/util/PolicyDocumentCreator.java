/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.util;

import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent.ProxyRequestContext;
import com.jaguarlandrover.d9.oneappbackend.lambda.library.dto.PolicyDocument;
import com.jaguarlandrover.d9.oneappbackend.lambda.library.dto.Statement;
import java.util.List;


public class PolicyDocumentCreator {

  public static final String VERSION = "2012-10-17";
  public static final String ACTION = "execute-api:Invoke";
  public static final String AWS_REGION = "AWS_REGION";
  private String policyArnFormat;

  public PolicyDocumentCreator(String policyArnFormat) {
    this.policyArnFormat = policyArnFormat;
  }

  /**
   * Constructor.
   */
  public PolicyDocument createPolicyDocument(String effect,
                                             APIGatewayProxyRequestEvent apiGatewayRequest) {

    return PolicyDocument.builder()
        .version(VERSION)
        .statements(List.of(createStatement(effect, apiGatewayRequest)))
        .build();
  }

  private Statement createStatement(String effect, APIGatewayProxyRequestEvent apiGatewayRequest) {

    return Statement.builder()
        .action(ACTION)
        .effect(effect)
        .resource(createArn(apiGatewayRequest))
        .build();
  }

  private String createArn(APIGatewayProxyRequestEvent apiGatewayRequest) {

    ProxyRequestContext proxyRequestContext = apiGatewayRequest.getRequestContext();
    return proxyRequestContext == null ? "" : createArn(proxyRequestContext);
  }

  private String createArn(ProxyRequestContext proxyRequestContext) {
    return String.format(this.policyArnFormat,
        System.getenv(AWS_REGION),
        proxyRequestContext.getAccountId(),
        proxyRequestContext.getApiId(),
        proxyRequestContext.getStage(),
        proxyRequestContext.getHttpMethod(),
        "*");
  }
}
