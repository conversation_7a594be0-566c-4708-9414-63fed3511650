/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.client;

import java.net.InetSocketAddress;
import lombok.SneakyThrows;
import net.spy.memcached.MemcachedClient;
import net.spy.memcached.internal.GetFuture;

public class OabMemcachedClient {

  private MemcachedClient memcachedClient;

  /**
   * Initialize the MemCached client.
   * @param endpoint endpoint of the MemCached to be connected
   * @param port port of the MemCached to be connected
   */
  @SneakyThrows
  public void initializeMemcachedClient(String endpoint, int port) {
    this.memcachedClient = new MemcachedClient(
        new InetSocketAddress(endpoint,
            port));
  }

  public GetFuture<Object> asyncGet(String key) {
    return this.memcachedClient.asyncGet(key);
  }

  public void set(String key, int expiryTime, String value) {
    memcachedClient.set(key, expiryTime, value);
  }

  public MemcachedClient getClient() {
    return memcachedClient;
  }
}
