/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.helper;

import com.amazonaws.secretsmanager.caching.SecretCache;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SecretsManagerHelper {

  private final SecretCache cache  = new SecretCache();

  /**
   * Get the secret key of Approov from AWS Secrets Manager.
   * @return the secret key of Approov
   */
  public String getApproovSecretKey() {

    String secretJsonString = cache.getSecretString("oab/approov");

    ObjectMapper mapper = new ObjectMapper();
    try {
      JsonNode rootNode = mapper.readTree(secretJsonString);
      return rootNode.get("approov.secret-key").asText();
    } catch (JsonProcessingException e) {
      log.error("Failed to get Approov secret key from secrets manager.", e);
      throw new RuntimeException(e);
    }
  }

}
