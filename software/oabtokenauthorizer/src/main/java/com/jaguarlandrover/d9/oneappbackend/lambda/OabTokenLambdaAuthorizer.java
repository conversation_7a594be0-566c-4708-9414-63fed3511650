/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda;

import static com.jaguarlandrover.d9.oneappbackend.lambda.service.CertificateService.CERT_SEPERATOR;

import com.amazonaws.services.lambda.runtime.Context;
import com.amazonaws.services.lambda.runtime.RequestHandler;
import com.amazonaws.services.lambda.runtime.events.APIGatewayProxyRequestEvent;
import com.jaguarlandrover.d9.oneappbackend.lambda.client.CachedAwsClient;
import com.jaguarlandrover.d9.oneappbackend.lambda.client.FrApiClient;
import com.jaguarlandrover.d9.oneappbackend.lambda.client.OabMemcachedClient;
import com.jaguarlandrover.d9.oneappbackend.lambda.factory.ObjectFactory;
import com.jaguarlandrover.d9.oneappbackend.lambda.helper.EnvironmentVariables;
import com.jaguarlandrover.d9.oneappbackend.lambda.helper.SecretsManagerHelper;
import com.jaguarlandrover.d9.oneappbackend.lambda.helper.TokenParser;
import com.jaguarlandrover.d9.oneappbackend.lambda.library.dto.AuthorizerOutput;
import com.jaguarlandrover.d9.oneappbackend.lambda.library.dto.PolicyDocument;
import com.jaguarlandrover.d9.oneappbackend.lambda.service.ApproovAuthorizationService;
import com.jaguarlandrover.d9.oneappbackend.lambda.service.CertificateService;
import com.jaguarlandrover.d9.oneappbackend.lambda.service.FrAuthorizationService;
import com.jaguarlandrover.d9.oneappbackend.lambda.service.TokenService;
import com.jaguarlandrover.d9.oneappbackend.lambda.util.PolicyDocumentCreator;
import java.io.IOException;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.CloseableThreadContext;


/**
 * Handler for requests to Token Lambda function.
 */
@Slf4j
public class OabTokenLambdaAuthorizer implements RequestHandler<APIGatewayProxyRequestEvent, AuthorizerOutput> {

  public static final String ALLOW = "Allow";
  public static final String DENY = "Deny";
  public static final String UNAUTHORIZED = "Unauthorized";

  private final FrApiClient frApiClient;
  private final PolicyDocumentCreator policyCreator;

  private final CachedAwsClient cachedAwsClient;
  private final EnvironmentVariables environmentVariables;

  private final SecretsManagerHelper secretsManagerHelper;

  // no args constructor for Lambda to work
  public OabTokenLambdaAuthorizer() throws IOException {
    this(new ObjectFactory(new EnvironmentVariables(), new OabMemcachedClient(),
            new SecretsManagerHelper()));
  }

  // injected dependencies to make code testable
  public OabTokenLambdaAuthorizer(EnvironmentVariables environmentVariables,
                                  OabMemcachedClient oabMemcachedClient,
                                  SecretsManagerHelper secretsManagerHelper) throws IOException {

    this(new ObjectFactory(environmentVariables, oabMemcachedClient, secretsManagerHelper));
  }

  /**
   * Constructor.
   */
  public OabTokenLambdaAuthorizer(ObjectFactory objectFactory) throws IOException {
    this.frApiClient = objectFactory.frApiClient();
    this.policyCreator = objectFactory.policyDocumentCreator();
    this.cachedAwsClient = objectFactory.cachedAwsClient();
    this.environmentVariables = objectFactory.environmentVariables();
    this.secretsManagerHelper = objectFactory.secretsManagerHelper();
  }

  @Override
  public AuthorizerOutput handleRequest(final APIGatewayProxyRequestEvent requestEvent,
                                        final Context context) {

    // Extract the requestId from context
    String requestId = getHttpRequestId(requestEvent);

    try (final CloseableThreadContext.Instance ignored = CloseableThreadContext.put("http.request_id", requestId)) {
      log.info("Received request with ID: {}", requestId);

      // 1. Approov token validation
      Optional<String> approovTokenOptional = getApproovAuthorizationToken(requestEvent);
      // Even if the configuration is disabled, perform the validation anyway if ATTESTATION header
      // is populated
      if ("true".equalsIgnoreCase(environmentVariables.getVariable("APPROOV_ENABLED"))
              || approovTokenOptional.isPresent()) {
        if (approovTokenOptional.isEmpty()) {
          log.warn("No Approov token present.");
          throw new RuntimeException(UNAUTHORIZED);
        }

        // Validate the Approov token
        ApproovAuthorizationService approovAuthorizationService = new ApproovAuthorizationService();
        String approovToken = approovTokenOptional.get();

        if (!approovAuthorizationService.isApproovTokenValid(approovToken, secretsManagerHelper.getApproovSecretKey())) {
          log.warn("Invalid Approov token.");
          throw new RuntimeException(UNAUTHORIZED);
        }
      }

      // 2. ForgeRock token validation
      Optional<String> frTokenOptional = getFrAuthorizationToken(requestEvent);
      if (frTokenOptional.isEmpty()) {
        log.warn("No ForgeRock token present.");
        throw new RuntimeException(UNAUTHORIZED);
      }

      // Get certificate from Memcached / ForgeRock service
      CertificateService certificateService = new CertificateService(frApiClient, cachedAwsClient);
      String certificate = certificateService.getCertificate();

      // Validate the FR token
      FrAuthorizationService authorizerService = new FrAuthorizationService();
      String token = frTokenOptional.map(s -> s.replaceAll("Bearer\\s+", "")).orElse("");
      String[] certificateArr = certificate.split(CERT_SEPERATOR);
      for (String singleCertificate : certificateArr) {
        if (authorizerService.isTokenValid(token, singleCertificate, environmentVariables)) {
          // Extract the attributes from the token
          return validationResult(token, requestEvent);
        }
      }
      log.warn("Invalid ForgeRock token.");
      throw new RuntimeException(UNAUTHORIZED);
    }
  }

  /**
   * Get the HTTP Request ID from the event.
   */
  private String getHttpRequestId(APIGatewayProxyRequestEvent event) {
    if (event == null || event.getRequestContext() == null) {
      return "";
    } else {
      return event.getRequestContext().getRequestId();
    }
  }

  private AuthorizerOutput validationResult(String token, final APIGatewayProxyRequestEvent requestEvent) {
    TokenParser tokenParser = new TokenParser();
    Map<String, String> attributes = tokenParser.retrieveTokenAttributes(token);
    String principalId = attributes.get(TokenParser.PRINCIPAL_ID_KEY);
    log.info("Principal ID: {}", principalId);
    String effect = DENY;
    if (principalId != null && !"".equalsIgnoreCase(principalId)) {
      effect = ALLOW;
    }
    return createAuthorizerOutput(requestEvent, effect, principalId, attributes);
  }

  private AuthorizerOutput createAuthorizerOutput(APIGatewayProxyRequestEvent requestEvent,
                                                  String effect,
                                                  String principalId,
                                                  Map<String, String> attributes) {
    PolicyDocument policyDocument = policyCreator.createPolicyDocument(effect, requestEvent);

    return AuthorizerOutput.builder()
        .principalId(principalId)
        .policyDocument(policyDocument)
        .context(attributes)
        .build();
  }

  private Optional<String> getFrAuthorizationToken(APIGatewayProxyRequestEvent requestEvent) {
    return new TokenService().getFrAuthorizationToken(requestEvent);
  }

  private Optional<String> getApproovAuthorizationToken(APIGatewayProxyRequestEvent requestEvent) {
    return new TokenService().getApproovAuthorizationToken(requestEvent);
  }

}
