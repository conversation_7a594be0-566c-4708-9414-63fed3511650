/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.lambda.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * Response from ForgeRock /jwk_uri endpoint.
 */
@Getter
@Setter
@NoArgsConstructor
public class FrJwtKey {

  // Key Type
  private String kty;

  // Key Identifier
  private String kid;

  // Usage – ‘sig’ for signing keys, ‘enc’ for encryption keys
  private String use;

  //  X.509 Certificate Thumbprint
  private String x5t;

  // X.509 Certificate Chain
  private List<String> x5c;

  @JsonProperty("n")
  private String n1;

  @JsonProperty("e")
  private String e1;

}
