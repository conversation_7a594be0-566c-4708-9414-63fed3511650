include:
  - project: "d9/infrastructure/gitlab-ci-templates"
    ref: master
    file: "/.lambda-java-cdk-gitlab-ci-template.yml"
  - project: "d9/infrastructure/gitlab-ci-templates"
    ref: master
    file: ".lambda-java-cdk-gitlab-ci-template-cn.patch.yml"
# It's necessary as custom cdk image doesn't contain the newest version of aws-cdk.
# Lack of newest version may cause some project to fail. I've tried to use 'cache', but couldn't achieve it.
.install_newest_aws_cdk: &install_newest_aws_cdk
  before_script:
    - npm install -g aws-cdk

variables:
  DEV_ENVIRONMENT: mobile-apps-backend-developers
  PRE_PROD_ENVIRONMENT: mobile-apps-backend-pre-production
  PROD_ENVIRONMENT: mobile-apps-backend-production
  CN_DEV_ENVIRONMENT: mobile-apps-backend-cn-dev
  CN_PREPROD_ENVIRONMENT: "mobile-apps-backend-cn-preprod"
  CN_PROD_ENVIRONMENT: "mobile-apps-backend-cn-prod"
  CN_DEV_PIPELINE_ROLE: "arn:aws-cn:iam::047063831595:role/cdk-pipeline-role"
  CN_PREPROD_PIPELINE_ROLE: "arn:aws-cn:iam::047071156672:role/cdk-pipeline-role"
  # CN_PROD_PIPELINE_ROLE: ""

stages:
  - install
  - validate
  - mutate
  - inspect code
  - config validation
  - docker build
  - inspect image
  - dev
  - dev test
  - staging
  - preprod
  - preprod_automation_tests
  - preprod test
  - production
  - production test
  - trigger_datadog

.mab-dev-runners: &mab-dev-runners
  tags:
    - aws
    - mab-developers

build_lambdas:
  artifacts:
    expire_in: 15min
    paths:
      - "**/target/commands-proxy-handler-*.jar"
      - "**/target/oab-token-authorizer-software-*.jar"
      - "**/target/eni-ip-lookup-*.jar"
      - "**/target/oab-lambda-library-software-*.jar"
  <<: *mab-dev-runners

cdk_synth:
  stage: validate
  image: $CUSTOM_CDK_IMAGE
  needs:
    - build_lambdas
  artifacts:
    expire_in: 15min
    paths:
      - "**/target/commands-proxy-handler-*.jar"
      - "**/target/oab-token-authorizer-software-*.jar"
      - "**/target/eni-ip-lookup-*.jar"
      - "**/target/oab-lambda-library-software-*.jar"
      - "infrastructure/cdk.out/*.json"
      - "infrastructure/cdk.out/cdk.out"
  <<: *mab-dev-runners

cdk_synth_cn:
  allow_failure: true
  artifacts:
    expire_in: 15min
    paths:
      - "**/target/commands-proxy-handler-*.jar"
      - "**/target/oab-token-authorizer-software-*.jar"
      - "**/target/eni-ip-lookup-*.jar"
      - "**/target/oab-lambda-library-software-*.jar"
      - "infrastructure/cdk.out.cn/*.json"
      - "infrastructure/cdk.out.cn/cdk.out"
  tags:
    - mobile-apps-backend-cn-dev
  when: manual

cdk_diff:
  extends: .install_newest_aws_cdk
  stage: validate
  image: $CUSTOM_CDK_IMAGE
  needs:
    - build_lambdas
    - cdk_synth
  <<: *mab-dev-runners
  script:
    - cd infrastructure
    - cdk diff
  environment:
    name: $DEV_ENVIRONMENT
  rules:
    - !reference [ .sonar_on_mr_rules, rules ]

test_lambdas:
  <<: *mab-dev-runners

test_infra:
  stage: validate
  image: $CUSTOM_CDK_IMAGE
  <<: *mab-dev-runners
  cache:
    key: $CI_COMMIT_REF_SLUG-$CI_PROJECT_DIR
    paths:
      - .m2/repository
    policy: pull
  script:
    - cd infrastructure/
    - mvn $MAVEN_CLI_OPTS test verify
  rules:
    - !reference [ .sonar_on_mr_rules, rules ]

mutation_test:
  <<: *mab-dev-runners
  before_script:
    - cd software/
    - mvn -pl library -am clean install
  script:
    - mvn $MAVEN_CLI_OPTS verify

snyk:
  before_script:
    - cd software/
    - mvn -pl library -am clean install

checkov:
  tags:
    - aws
    - vcdp-dev

# Not 100% sure about that stage - needs time to validate if adds some benefit
cdk_doctor:
  <<: *mab-dev-runners

.deploy:
  <<: *mab-dev-runners

deploy_aws_dev:
  <<: *mab-dev-runners

deploy_aws_preprod:
  tags:
    - aws
    - mab-pre-production

deploy_aws_prod:
  tags:
    - aws
    - mab-production

deploy_aws_dev_cn:
  dependencies:
    - cdk_synth_cn
  tags:
    - mobile-apps-backend-cn-dev
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: "$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH"
      when: on_success

deploy_aws_preprod_cn:
  dependencies:
    - cdk_synth_cn
  tags:
    - mobile-apps-backend-cn-preprod
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: "$CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH"
      when: manual
