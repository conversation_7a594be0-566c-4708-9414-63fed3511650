#
# Copyright (c) Jaguar Land Rover Ltd 2025. All rights reserved
#
# Use this script to verify the project before deployment.
#
#!/usr/bin/env bash

################################################################################
export AWS_PROFILE=mab-dev
# The IPs can be found in GitLab repo: Settings -> CD/CD Settings -> Variables
export ECO_INT_VPCE_IPS=<IPs of the Ecosystem Integration VPC endpoint>
export ENV=mobile-apps-backend-developers
################################################################################

mvn clean -f software/ package shade:shade -D skipTests

cd software
mvn verify -D dependency-check.skip=true

cd ../infrastructure
mvn test verify

aws sso login --profile $AWS_PROFILE
CI_ENVIRONMENT_NAME=$ENV ECO_INT_VPCE_IPS=$ECO_INT_VPCE_IPS cdk synth --profile $AWS_PROFILE

checkov --directory . --quiet

cd ..
trivy fs . --scanners vuln,secret,config

cd ./software
snyk test --maven-aggregate-project
