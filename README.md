# OneApp Backend API Gateway

## Overview

This is an AWS CDK project which creates an AWS API Gateway to all OneApp Backend microservices with a Lambda Authorizer function.

It is a Maven based project, so you can open this project with any Maven compatible Java IDE to build and run tests.

The cdk.json file tells the CDK Toolkit how to execute the application.

## Project structure

    ├── infrastructure   
        ├── src                              # Infrastructure as a code via CDK (Java)
    └── software                             # Holds business logic (Java) 
        ├── EniIpLookup                      # Helping function to get network interfaces ips (Java) 
        ├── OabTokenLambdaAuthorizer         # Lambda Authorizer function (Java)
        ├── OabCommandsRequestHandlerProxy   # Lambda function calling Command Router (Java)
        └── library                          # Contains shared classes

In order to use the following cdk commands you need to have AWS CDK CLI installed:
```
npm install -g aws-cdk
```
Run the following command to verify a successful installation. The AWS CDK CLI should output the version number:
```
cdk --version
```

## Useful commands

| Command                       | Description                                          |
|-------------------------------|------------------------------------------------------|
| mvn package                   | compile and run tests                                |
| cdk ls                        | list all stacks in the app                           |
| cdk synth                     | emits the synthesized CloudFormation template        |
| cdk deploy                    | deploy this stack to your default AWS account/region |
| cdk diff                      | compare deployed stack with current state            |
| cdk docs                      | open CDK documentation                               |
| checkov --directory . --quiet | analyze IaC scan results                             |


## Testing

To run infrastructure code tests locally:
```
cd infrastructure
mvn test
```

## Verification
Run the below command to verify the project.
This will run all the tests, skipping the slow dependency checking, generate a report file for code coverage and check code style.
```
mvn verify -D dependency-check.skip=true
```
To see the code coverage report open `target/site/jacoco/index.html` on your web browser.

## Run these steps to test before commit

Pre-requisites:
1. [Install Checkov](https://www.checkov.io/2.Basics/Installing%20Checkov.html).
2. [Install Trivy](https://trivy.dev/latest/getting-started/installation/).
3. [Install Snyk CLI](https://docs.snyk.io/snyk-cli/install-or-update-the-snyk-cli).

This section lists the steps required before committing to GitLab to reduce chances of pipeline failure. You can also run `./verify.sh` to perform the following steps.

1. Package the lambda functions in software/ directory.
    ```shell
    mvn clean -f software/ package shade:shade -D skipTests
    ```

2. Perform verification and mutation test.
    ```shell
    cd software
    mvn verify -D dependency-check.skip=true
    ```
 
3. Run infrastructure code tests.
    ```shell
    cd ../infrastructure
    mvn test verify
    ```

4. Explore generated CloudFormation template.
    ```shell
    export AWS_PROFILE=mab-dev
    export ECO_INT_VPCE_IPS=<IPs of the Ecosystem Integration VPC endpoint>
   
    aws sso login --profile $AWS_PROFILE
    CI_ENVIRONMENT_NAME=mobile-apps-backend-developers ECO_INT_VPCE_IPS=$ECO_INT_VPCE_IPS cdk synth --profile $AWS_PROFILE
    ```

5. Analyze infrastructure as code (IaC) scan results.
    ```shell
    checkov --directory . --quiet
    ```

6. Scan for vulnerabilities using Trivy locally.
    ```shell
    cd ..
    trivy fs . --scanners vuln,secret,config
    ```

7. Scan for vulnerabilities using Snyk locally.
    ```shell
    cd ./software
    snyk test --maven-aggregate-project
    ```
   
## Deployment

To deploy to AWS manually (i.e. without pipeline),

1. Package the lambda functions in software/ directory.
    ```shell
    mvn -f software/ package shade:shade
    ```

2. Login AWS SSO using AWS CLI (change `AWS_PROFLIE` to your desired AWS profile name).
    ```shell
    export AWS_PROFILE=mab-dev
    aws sso login --profile $AWS_PROFILE
    ```
   
3. Generate CloudFormation template.
    ```shell
    cd infrastructure
    CI_ENVIRONMENT_NAME=mobile-apps-backend-developers cdk synth --profile $AWS_PROFILE
    ```

4. Deploy to AWS.
    ```shell
    CI_ENVIRONMENT_NAME=mobile-apps-backend-developers cdk deploy --profile $AWS_PROFILE
    ```

    For more than one stack,
    ```shell
    CI_ENVIRONMENT_NAME=mobile-apps-backend-developers cdk deploy --all --profile $AWS_PROFILE
    ```


## License and copyright

Copyright (c) 2023. Jaguar Land Rover - All Rights Reserved.

CONFIDENTIAL INFORMATION - DO NOT DISTRIBUTE
