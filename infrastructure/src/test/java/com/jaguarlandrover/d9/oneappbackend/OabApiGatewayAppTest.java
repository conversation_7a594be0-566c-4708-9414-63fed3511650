/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend;

import org.junit.jupiter.api.Test;
import org.junitpioneer.jupiter.SetEnvironmentVariable;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;


class OabApiGatewayAppTest {

  /**
   * Junit-pioneer for setting environment variables only works in Java.
   */
  @Test
  @SetEnvironmentVariable(key = "CI_ENVIRONMENT_NAME", value = "mobile-apps-backend-developers")
  @SetEnvironmentVariable(key = "ECO_INT_VPCE_IPS", value = "*********:*********")
  void testApp() {
    assertDoesNotThrow(() -> OabApiGatewayApp.main(null));
  }

}
