/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend

class OabApiGatewayProdStackSpec extends OabApiGatewayStackSpecification {

    @Override
    def setEnvName() {
        this.envName = "mobile-apps-backend-production"
    }

    def "validate API Gateway Resources in Production"() {
        expect:
        template.resourceCountIs("AWS::ApiGateway::Resource", 72)

        "common tests for validating resources"()
    }

    def "validate API Gateway Methods in Production"() {
        expect:
        template.resourceCountIs("AWS::ApiGateway::Method", 72)

        "common tests for validating methods"()
    }

    def "validate lambda permission Production"() {
        expect:
        template.resourceCountIs("AWS::Lambda::Permission", 9)

        template.hasResourceProperties("AWS::Lambda::Permission", Map.of(
                "Action", "lambda:InvokeFunction",
                "Principal", "apigateway.amazonaws.com"
        ))
    }

}
