/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend

import com.jaguarlandrover.d9.oneappbackend.config.OabApiGatewayAppConfig
import com.jaguarlandrover.d9.oneappbackend.config.OabApiGatewaySystemConfig
import com.jaguarlandrover.d9.oneappbackend.util.AppConfigLoader
import com.jaguarlandrover.d9.oneappbackend.util.OabDatadogHelper
import java.util.stream.Stream
import org.junit.jupiter.api.Assertions
import software.amazon.awscdk.App
import software.amazon.awscdk.AppProps
import software.amazon.awscdk.Environment
import software.amazon.awscdk.StackProps
import software.amazon.awscdk.assertions.Template
import software.amazon.awscdk.services.apigateway.EndpointType
import software.amazon.awscdk.services.apigateway.MethodLoggingLevel
import software.amazon.awscdk.services.lambda.LogFormat
import software.amazon.awscdk.services.logs.RetentionDays
import spock.lang.Requires
import spock.lang.Specification


import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.*

class OabApiGatewayStackSpecification extends Specification {

    static final String PROD_ENV = "mobile-apps-backend-production"

    static OabApiGatewayAppConfig appConfig
    static OabApiGatewaySystemConfig systemConfig

    static OabApiGatewayStack stack

    static Template template

    static Object[] ddApiGwTags
    static Object[] ddTagsForLambdaService

    static String envName

    def setEnvName() {
        this.envName = "mobile-apps-backend-developers"
    }

    def getEnvName() {
        return this.envName
    }

    def setupSpec() {
        setEnvName()

        App app = new App(AppProps.builder().build())

        appConfig = AppConfigLoader.load(this.envName)

        final String ddVersion = OabDatadogHelper.generateDdVersion()

        // IP addresses, colon separated
        final List<String> ecoIntVpceIps = List.of("*********:*********")

        systemConfig = OabApiGatewaySystemConfig.builder()
                .ddVersion(ddVersion)
                .ecoIntVpceIps(ecoIntVpceIps)
                .build()

        stack = new OabApiGatewayStack(app,
                "test",
                StackProps.builder()
                        .env(Environment.builder()
                                .account("account-id")
                                .region("region")
                                .build())
                        .build(),
                appConfig,
                systemConfig)

        template = Template.fromStack(stack)

        // Datadog tags for API Gateway
        ddApiGwTags = Stream.of(
                Map.of("Key", "env",
                        "Value", appConfig.getDdEnv()),
                Map.of("Key", "managed_by",
                        "Value", appConfig.getDdManagedBy()),
                Map.of("Key", "pii_data_handler",
                        "Value", appConfig.getDdPiiDataHandler()),
                Map.of("Key", "product_owner",
                        "Value", appConfig.getDdProductOwner()),
                Map.of("Key", "repo_url",
                        "Value", appConfig.getDdRepoUrl()),    
                Map.of("Key", "service",
                        "Value", appConfig.getDdService()),
                Map.of("Key", "squad",
                        "Value", appConfig.getDdSquad()),
                Map.of("Key", "version",
                        "Value", ddVersion)).toArray()

        // Datadog tags for Lambda Authorizer
        ddTagsForLambdaService = Stream.of(
                Map.of("Key", "env",
                        "Value", appConfig.getDdEnv()),
                Map.of("Key", "managed_by",
                        "Value", appConfig.getDdManagedBy()),
                Map.of("Key", "pii_data_handler",
                        "Value", appConfig.getDdPiiDataHandler()),
                Map.of("Key", "product_owner",
                        "Value", appConfig.getDdProductOwner()),
                Map.of("Key", "repo_url",
                        "Value", appConfig.getDdRepoUrl()),
                Map.of("Key", "service",
                        "Value", appConfig.getDdServiceLambdaAuthorizer()),
                Map.of("Key", "squad",
                        "Value", appConfig.getDdSquad()),
                Map.of("Key", "version",
                        "Value", ddVersion)).toArray()
    }

    def "template should not null"() {
        expect:
        Assertions.assertNotNull(template)
    }

    void "common tests for validating resources"() {
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "api"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "v1"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "users"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "me"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "features"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "vehicles"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "{vehicleId}"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "vehicles"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "trips"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "{tripId}"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "charging"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "home"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "chargers"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "{chargerId}"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "start"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "stop"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "status"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "{proxy+}"))
    }

    void "common tests for validating methods"() {
        // 0. Authorizer
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
            "AuthorizationType", "CUSTOM",
            "HttpMethod", "POST",
            "Integration", Map.of(
            "IntegrationHttpMethod", "POST",
            "Type", "AWS_PROXY"
            )
        ))

        // 1. User Registration
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
            "AuthorizationType", "CUSTOM",
            "HttpMethod", "POST",
            "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "POST",
                "RequestParameters", Map.of(
                    "integration.request.path.id", "context.authorizer.principalId"
                    ),
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getUserServiceUsersRegisterEndpointUrl()
            )
        ))

        // 2. Users ME Get
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
            "AuthorizationType", "CUSTOM",
            "HttpMethod", "GET",
            "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "GET",
                "RequestParameters", Map.of(
                    "integration.request.path.id", "context.authorizer.principalId"
                    ),
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getUserServiceUsersEndpointUrl()
            )
        ))

        // 3a. Users ME Update
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
                "AuthorizationType", "CUSTOM",
                "HttpMethod", "PUT",
                "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "PUT",
                "RequestParameters", Map.of(
                "integration.request.path.id", "context.authorizer.principalId"
        ),
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getUserServiceUsersUpdateEndpointUrl()
        )
        ))

        // 3b. Users ME Delete
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
                "AuthorizationType", "CUSTOM",
                "HttpMethod", "DELETE",
                "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "DELETE",
                "RequestParameters", Map.of(
                "integration.request.path.id", "context.authorizer.principalId"
        ),
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getUserServiceUsersDeleteEndpointUrl()
        )
        ))

        // 4. Users Retrieve Consents
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
                "AuthorizationType", "CUSTOM",
                "HttpMethod", "GET",
                "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "GET",
                "RequestParameters", Map.of(
                    "integration.request.path.id", "context.authorizer.principalId"
                    ),
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getUserServiceUsersRetrieveConsentsEndpointUrl()
            )
        ))

        // 5. Users Update Consents
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
                "AuthorizationType", "CUSTOM",
                "HttpMethod", "PATCH",
                "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "PATCH",
                "RequestParameters", Map.of(
                    "integration.request.path.id", "context.authorizer.principalId"
                    ),
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getUserServiceUsersUpdateConsentsEndpointUrl()
                    )
        ))

        // 6. Get my features
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
            "AuthorizationType", "CUSTOM",
            "HttpMethod", "GET",
            "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "GET",
                "RequestParameters", Map.of(
                        "integration.request.path.id", "context.authorizer.principalId"
                ),
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getFeatureServiceMeEndpointUrl()
            )
        ))

        // 7. Get my vehicles
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
            "AuthorizationType", "CUSTOM",
            "HttpMethod", "GET",
            "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "GET",
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getVehicleServiceMyVehiclesEndpointUrl()
            )
        ))

        // 8. Get my trips
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
            "AuthorizationType", "CUSTOM",
            "HttpMethod", "GET",
            "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "GET",
                "RequestParameters", Map.of(
                        "integration.request.path.vehicleId", "method.request.path.vehicleId"
                ),
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getTripServiceMeEndpointUrl()
            )
        ))

        // 9. Get my trip by trip ID
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
            "AuthorizationType", "CUSTOM",
            "HttpMethod", "GET",
            "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "GET",
                "RequestParameters", Map.of(
                    "integration.request.path.vehicleId", "method.request.path.vehicleId",
                    "integration.request.path.tripId", "method.request.path.tripId"
                ),
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getTripDetailsServiceMeEndpointUrl()
            )
        ))

        // 10. Home Charging GET
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
            "AuthorizationType", "CUSTOM",
            "HttpMethod", "GET",
            "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "GET",
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getHomeChargingServiceChargersEndpointUrl()
            )
        ))

        // 11. Home Charging POST
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
            "AuthorizationType", "CUSTOM",
            "HttpMethod", "POST",
            "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "POST",
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getHomeChargingServiceChargersEndpointUrl()
            )
        ))

        // 12. Home Charging Start
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
            "AuthorizationType", "CUSTOM",
            "HttpMethod", "POST",
            "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "POST",
                "RequestParameters", Map.of(
                    "integration.request.path.chargerId", "method.request.path.chargerId"
                ),
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getHomeChargingServiceChargersStartEndpointUrl()
            )
        ))

        // 13. Home Charging Stop
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
            "AuthorizationType", "CUSTOM",
            "HttpMethod", "POST",
            "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "POST",
                "RequestParameters", Map.of(
                    "integration.request.path.chargerId", "method.request.path.chargerId"
                ),
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getHomeChargingServiceChargersStopEndpointUrl()
            )
        ))

        // 14. Home Charging Get Status
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
            "AuthorizationType", "CUSTOM",
            "HttpMethod", "GET",
            "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "GET",
                "RequestParameters", Map.of(
                    "integration.request.path.chargerId", "method.request.path.chargerId"
                ),
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getHomeChargingServiceChargersStatusEndpointUrl()
            )
        ))

        // 15. Other endpoints are invalid
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
            "AuthorizationType", "NONE",
            "HttpMethod", "ANY",
            "Integration", Map.of(
                "PassthroughBehavior", "NEVER"
            )
        ))

        // 16. CMIS FAQS GET
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
                "AuthorizationType", "CUSTOM",
                "HttpMethod", "GET",
                "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "GET",
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getCustomerApiFindAddressEndpointUrl()
        )
        ))

//        // 17. Device Verifications POST
//        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
//                "AuthorizationType", "CUSTOM",
//                "HttpMethod", "POST",
//                "Integration", Map.of(
//                "ConnectionType", "VPC_LINK",
//                "IntegrationHttpMethod", "POST",
//                "RequestParameters", Map.of(
//                "integration.request.header.x-amzn-RequestId", "context.requestId"
//        ),
//                "Type", "HTTP_PROXY",
//                "Uri", appConfig.getDeviceVerificationServiceEndpointUrl()
//        )
//        ))
    }

    /** start validating stack infrastructures below */

    def "validate log groups"() {
        expect:
        template.resourceCountIs("AWS::Logs::LogGroup", 2)

        // 1. Log Group for API Gateway
        template.hasResourceProperties("AWS::Logs::LogGroup", Map.of(
                "LogGroupName", APIGW_LOG_GROUP_NAME,
                "RetentionInDays", RetentionDays.ONE_WEEK,
                "Tags", ddApiGwTags
        ))

        // 2. Log Group for ForgeRock Lambda Authorizer
        template.hasResourceProperties("AWS::Logs::LogGroup", Map.of(
                "LogGroupName", "/custom/one-app-backend-fr-authorizer-function",
                "RetentionInDays", RetentionDays.ONE_WEEK,
                "Tags", ddTagsForLambdaService
        ))
    }

    def "validate KMS keys"() {
        expect:
        template.resourceCountIs("AWS::KMS::Key", 2)

        template.hasResourceProperties("AWS::KMS::Key", Map.of(
                "EnableKeyRotation", true,
                "Tags", ddApiGwTags
        ))
    }

    def "validate sqs queue for lambda DLQ"() {
        expect:
        template.resourceCountIs("AWS::SQS::Queue", 1)
        template.hasResourceProperties("AWS::SQS::Queue", Map.of(
                "Tags", ddApiGwTags
        ))
        template.hasResource("AWS::SQS::Queue", Map.of(
                "UpdateReplacePolicy", "Delete",
                "DeletionPolicy", "Delete"
        ))
    }

    def "validate API Gateway"() {
        expect:
        // 1. API Gateway
        template.resourceCountIs("AWS::ApiGateway::RestApi", 1)
        template.hasResourceProperties("AWS::ApiGateway::RestApi", Map.of(
                "Name", APIGW_NAME,
                "Description", APIGW_DESC,
                "EndpointConfiguration", Map.of(
                "Types", List.of(EndpointType.REGIONAL)
        ),
                "Tags", ddApiGwTags
        ))
    }

    def "validate deployment stage of the API Gateway"() {
        expect:
        template.resourceCountIs("AWS::ApiGateway::Stage", 1)

        template.hasResourceProperties("AWS::ApiGateway::Stage", Map.of(
                "StageName", appConfig.getDeploymentStage(),
                "TracingEnabled", false,
                "MethodSettings", Stream.of(Map.of(
                        "CachingEnabled", false,
                        "DataTraceEnabled", false,
                        "HttpMethod", "*",
                        "LoggingLevel", "INFO",
                        "MetricsEnabled", true,
                        "ResourcePath", "/*"
                    )).toArray(),
                "Tags", ddApiGwTags
        ))
    }

    def "validate IAM roles"() {
        expect:
        template.resourceCountIs("AWS::IAM::Role", 5)

        // 1. Role for push to CloudWatch logs
        template.hasResourceProperties("AWS::IAM::Role", Map.of(
            "AssumeRolePolicyDocument", Map.of(
                    "Version", "2012-10-17",
                    "Statement", Collections.singletonList(Map.of(
                            "Action", "sts:AssumeRole",
                            "Effect", "Allow",
                            "Principal", Collections.singletonMap("Service", "apigateway.amazonaws.com")
                    ))),
            "ManagedPolicyArns", Collections.singletonList(Map.of(
                "Fn::Join", Arrays.asList(
                        "",
                        Arrays.asList("arn:",
                                Map.of("Ref", "AWS::Partition"),
                                ":iam::aws:policy/service-role/AmazonAPIGatewayPushToCloudWatchLogs"))
                    )),
            "Tags", ddApiGwTags
            )
        )

        // 2. Role for executing lambda
        template.hasResourceProperties("AWS::IAM::Role", Map.of(
            "AssumeRolePolicyDocument", Map.of(
                "Version", "2012-10-17",
                "Statement", Collections.singletonList(Map.of(
                    "Action", "sts:AssumeRole",
                    "Effect", "Allow",
                    "Principal", Collections.singletonMap("Service", "lambda.amazonaws.com")
                ))),
            "ManagedPolicyArns", Collections.singletonList(Map.of(
                "Fn::Join", Arrays.asList(
                    "",
                    Arrays.asList("arn:",
                        Map.of("Ref", "AWS::Partition"),
                        ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"))
                )),
            "Tags", ddApiGwTags
    )
        )
    }

    def "validate MemCached cluster"() {
        expect:
        template.resourceCountIs("AWS::ElastiCache::CacheCluster", 1)

        template.hasResourceProperties("AWS::ElastiCache::CacheCluster", Map.of(
                "CacheNodeType", "cache.t2.micro",
                "Engine", "memcached",
                "NumCacheNodes", appConfig.getCacheNodeCount(),
                "AZMode", "cross-az",
                "EngineVersion", "1.6.17",
                "NetworkType", "ipv4",
                "Port", 11211
        ))
    }

    def "validate lambda function"() {
        expect:
        template.resourceCountIs("AWS::Lambda::Function", 4)

        // ForgeRock Lambda Authorizer function
        template.hasResourceProperties("AWS::Lambda::Function", Map.of(
                "Environment", Collections.singletonMap(
                        "Variables", Map.of(
                            "APPROOV_ENABLED", appConfig.getApproovEnabled(),
                            "CACHE_ENDPOINT", Collections.singletonMap("Fn::GetAtt", Arrays.asList(
                                    "oneappmemcached",
                                    "ConfigurationEndpoint.Address"
                                )),
                            "CACHE_PORT", Collections.singletonMap("Fn::GetAtt", Arrays.asList(
                                    "oneappmemcached",
                                    "ConfigurationEndpoint.Port"
                                )),
                            "DD_JMXFETCH_ENABLED", "false",
                            "DD_LOGS_INJECTION", "true",
                            "DD_TRACE_ENABLED", "true",
                            "FORGEROCK_HOST", appConfig.getForgeRockHost()
                        )),
                "FunctionName", "one-app-backend-api-gateway-fr-authorizer-function",
                "Handler", "com.jaguarlandrover.d9.oneappbackend.lambda.OabTokenLambdaAuthorizer",
                "LoggingConfig", Map.of(
                    "ApplicationLogLevel", MethodLoggingLevel.INFO,
                    "LogFormat", LogFormat.JSON),
                "MemorySize", 1024,
                "ReservedConcurrentExecutions", 20,
                /*"Runtime", Runtime.JAVA_17.toString(),*/
                "SnapStart", Map.of("ApplyOn", "PublishedVersions"),
                "Tags", ddTagsForLambdaService
           /*     "Timeout", 120*/
        ))

        // Enii Lookup Lambda function
        template.hasResourceProperties("AWS::Lambda::Function", Map.of(
                "Handler", "com.jaguarlandrover.d9.oneappbackend.lambda.EniIpLookup",
                "Tags", ddTagsForLambdaService
        ))

        // Log retention Lambda function
        template.hasResourceProperties("AWS::Lambda::Function", Map.of(
                "Handler", "index.handler",
                "Tags", ddTagsForLambdaService
        ))
    }

    @Requires( { instance.getEnvName() != instance.PROD_ENV } )
    def "validate lambda permission Dev"() {
        expect:
        template.resourceCountIs("AWS::Lambda::Permission", 11)

        template.hasResourceProperties("AWS::Lambda::Permission", Map.of(
                "Action", "lambda:InvokeFunction",
                "Principal", "apigateway.amazonaws.com"
        ))
    }

    def "validate API Gateway Authorizer"() {
        expect:
        template.resourceCountIs("AWS::ApiGateway::Authorizer", 1)

        // ForgeRock Lambda Authorizer function
        template.hasResourceProperties("AWS::ApiGateway::Authorizer", Map.of(
                "Name", "one-app-backend-api-gateway-fr-authorizer",
                "IdentitySource", "method.request.header.Authorization"
        ))
    }

    def "validate VPC Links"() {

        expect:
        template.resourceCountIs("AWS::ApiGateway::VpcLink", 3)

        // vpcLink should point to the correct network load balancer
        template.hasResource("AWS::ApiGateway::VpcLink", Map.of())

    }

}
