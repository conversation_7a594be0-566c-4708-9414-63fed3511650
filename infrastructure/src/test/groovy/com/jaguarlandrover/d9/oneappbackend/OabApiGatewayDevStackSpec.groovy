/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend

import software.amazon.awscdk.services.apigateway.EndpointType

class OabApiGatewayDevStackSpec extends OabApiGatewayStackSpecification {

    @Override
    def setEnvName() {
        this.envName = "mobile-apps-backend-developers"
    }

    def "validate API Gateway Resources in Dev"() {
        expect:
        template.resourceCountIs("AWS::ApiGateway::Resource", 85)

        "common tests for validating resources"()
        "dev tests for validating paak resources"()
    }

    def "validate API Gateway Methods in Dev"() {
        expect:
        template.resourceCountIs("AWS::ApiGateway::Method", 81)

        "common tests for validating methods"()
        "dev tests for validating paak methods"()
    }

    def "validate custom domain name in Dev"() {
        expect:
        template.resourceCountIs("AWS::ApiGateway::DomainName", 2)
        template.hasResourceProperties("AWS::ApiGateway::DomainName", Map.of(
                "DomainName", appConfig.getCustomName().concat(".").concat(appConfig.getDomainName()),
                "EndpointConfiguration", Collections.singletonMap("Types", Arrays.asList(EndpointType.REGIONAL)),
                "RegionalCertificateArn", appConfig.getCertificate(),
                "Tags", ddApiGwTags
        ))
    }

    void "dev tests for validating paak resources"() {
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "pairing"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "{slotId}"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "keys"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "physical"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "resume"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "suspend"))
        template.hasResourceProperties("AWS::ApiGateway::Resource", Map.of("PathPart", "identity-mapping"))
    }

    void "dev tests for validating paak methods"() {
        // getPhysicalKeys
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
            "AuthorizationType", "CUSTOM",
            "HttpMethod", "GET",
            "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "GET",
                "RequestParameters", Map.of(
                    "integration.request.path.vehicleId", "method.request.path.vehicleId"
                    ),
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getPaakGetPhysicalKeysEndpointUrl()
            )
        ))

        // pairing
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
            "AuthorizationType", "CUSTOM",
            "HttpMethod", "POST",
            "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "POST",
                "RequestParameters", Map.of(
                    "integration.request.path.vehicleId", "method.request.path.vehicleId"
                    ),
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getPaakOwnerPairingEndpointUrl()
            )
        ))

        // resume
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
            "AuthorizationType", "CUSTOM",
            "HttpMethod", "POST",
            "Integration", Map.of(
            "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "POST",
                "RequestParameters", Map.of(
                    "integration.request.path.vehicleId", "method.request.path.vehicleId",
                    "integration.request.path.slotId", "method.request.path.slotId"
                ),
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getPaakResumeKeyEndpointUrl()
            )
        ))

        // suspend key
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
            "AuthorizationType", "CUSTOM",
            "HttpMethod", "POST",
            "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "POST",
                "RequestParameters", Map.of(
                    "integration.request.path.vehicleId", "method.request.path.vehicleId",
                    "integration.request.path.slotId", "method.request.path.slotId"
                ),
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getPaakSuspendKeyEndpointUrl()
            )
        ))

        // identity-mapping
        template.hasResourceProperties("AWS::ApiGateway::Method", Map.of(
                "AuthorizationType", "CUSTOM",
                "HttpMethod", "POST",
                "Integration", Map.of(
                "ConnectionType", "VPC_LINK",
                "IntegrationHttpMethod", "POST",
                "Type", "HTTP_PROXY",
                "Uri", appConfig.getPaakVehicleIdentityMappingEndpointUrl()
        )
        ))
    }
}