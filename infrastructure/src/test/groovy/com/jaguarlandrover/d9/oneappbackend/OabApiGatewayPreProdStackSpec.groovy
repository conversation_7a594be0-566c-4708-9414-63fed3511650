/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend

class OabApiGatewayPreProdStackSpec extends OabApiGatewayStackSpecification {

    @Override
    def setEnvName() {
        this.envName = "mobile-apps-backend-pre-production"
    }

    def "validate API Gateway Resources in Pre-Production"() {
        expect:
        template.resourceCountIs("AWS::ApiGateway::Resource", 85)

        "common tests for validating resources"()
    }

    def "validate API Gateway Methods in Pre-Production"() {
        expect:
        template.resourceCountIs("AWS::ApiGateway::Method", 81)

        "common tests for validating methods"()
    }

}
