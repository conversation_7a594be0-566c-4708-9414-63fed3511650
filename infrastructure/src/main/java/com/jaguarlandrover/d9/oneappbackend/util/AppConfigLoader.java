/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.jaguarlandrover.d9.oneappbackend.config.OabApiGatewayAppConfig;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class AppConfigLoader {

  /**
   * Load configurations from YAML file.
   * @param configEnv environment name for the configurations
   *                  This function loads the configuration file specified by the configEnv
   *                  with ".yaml" extension from the "resources/config" directory.
   * @return configurations
   */
  public static OabApiGatewayAppConfig load(String configEnv) throws FileNotFoundException {

    if (configEnv == null || configEnv.isEmpty()) {
      throw new IllegalArgumentException("No environment is provided.");
    }
    try {
      File file = new File(Thread.currentThread().getContextClassLoader()
          .getResource(String.format("config/%s.yaml", configEnv)).getFile());

      // Instantiating a new ObjectMapper as a YAMLFactory
      ObjectMapper om = new ObjectMapper(new YAMLFactory());

      return om.readValue(file, OabApiGatewayAppConfig.class);
    } catch (UnrecognizedPropertyException e) {
      log.error("Error when parsing the configuration file", e);
      throw new FileNotFoundException("Error when parsing the configuration file.");
    } catch (IOException e) {
      throw new FileNotFoundException("No configuration file found.");
    }
  }

}
