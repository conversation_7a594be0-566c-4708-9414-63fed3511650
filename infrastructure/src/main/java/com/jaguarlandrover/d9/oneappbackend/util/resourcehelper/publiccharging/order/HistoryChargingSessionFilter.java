package com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.order;

import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.BaseEndpoint;
import java.util.HashMap;
import java.util.Map;
import software.amazon.awscdk.services.apigateway.Resource;

public class HistoryChargingSessionFilter extends BaseEndpoint {

  private final String methodFilter = method + pathParameter + "filter";
  private final String methodStartTime = method + queryParameter + "startTime";
  private final String methodEndTime = method + queryParameter + "endTime";
  private final String methodBrand = method + queryParameter + "brand";
  private final String methodVehicleId = method + queryParameter + "vehicleId";

  private final String integrationFilter = integration + pathParameter + "filter";
  private final String integrationStartTime = integration + queryParameter + "startTime";
  private final String integrationEndTime = integration + queryParameter + "endTime";
  private final String integrationBrand = integration + queryParameter + "brand";
  private final String integrationVehicleId = integration + queryParameter + "vehicleId";

  public HistoryChargingSessionFilter(Resource version, String targetUrl, String httpMethod) {
    super(version, targetUrl, httpMethod);
  }

  @Override
  protected Map<String, Boolean> initializeParamAreRequired() {
    Map<String, Boolean> map = new HashMap<>();
    map.put(methodFilter, true);
    map.put(methodStartTime, true);
    map.put(methodEndTime, true);
    map.put(methodBrand, true);
    map.put(methodVehicleId, true);
    return map;
  }

  @Override
  protected Map<String, String> initializeParamToBePassed() {
    Map<String, String> map = new HashMap<>();
    map.put(integrationFilter, methodFilter);
    map.put(integrationStartTime, methodStartTime);
    map.put(integrationEndTime, methodEndTime);
    map.put(integrationBrand, methodBrand);
    map.put(integrationVehicleId, methodVehicleId);
    return map;
  }
}
