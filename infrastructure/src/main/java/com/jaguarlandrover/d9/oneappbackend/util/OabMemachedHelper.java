/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.util;

import com.jaguarlandrover.d9.oneappbackend.config.OabApiGatewayAppConfig;
import java.util.List;
import software.amazon.awscdk.services.elasticache.CfnCacheCluster;
import software.amazon.awscdk.services.elasticache.CfnSubnetGroup;
import software.constructs.Construct;


public class OabMemachedHelper {

  private static final String CACHE_TYPE = "cache.t2.micro";
  private static final String ENGINE = "memcached";
  private static final String ENGINE_VERSION = "1.6.17";
  private static final String AZ_MODE = "cross-az";
  public static final int MEMCACHED_PORT = 11211;

  // Connectivity
  private static final String NETWORK_TYPE = "ipv4";

  // Names / Descriptions for OneApp Backend Memcached resources
  public static final String MEMCACHED_CLUSTER_NAME = "oneapp-memcached";
  public static final String MEMCACHED_SUBNET_GROUP_NAME = MEMCACHED_CLUSTER_NAME
      + "-subnet-group";
  public static final String MEMCACHED_SUBNET_GROUP_DESC = "Subnet group for "
      + MEMCACHED_CLUSTER_NAME;
  public static final String MEMCACHED_SG_NAME = MEMCACHED_CLUSTER_NAME + "-sg";
  public static final String MEMCACHED_SG_DESC = "Security group for "
      + MEMCACHED_CLUSTER_NAME;

  /**
   * Create Memcached Cluster.
   * @param scope scope
   */
  public static CfnCacheCluster createCacheCluster(Construct scope,
                                                   String clusterName,
                                                   String subnetGroupName,
                                                   String subnetGroupDesc,
                                                   List<String> vpcSecurityGroupIds,
                                                   OabApiGatewayAppConfig config) {
    CfnSubnetGroup cfnSubnetGroup = CfnSubnetGroup.Builder.create(scope, subnetGroupName)
        .cacheSubnetGroupName(subnetGroupName)
        .description(subnetGroupDesc)
        .subnetIds(config.getSubnetIds())
        .build();

    return CfnCacheCluster.Builder.create(scope, clusterName)
        .clusterName(clusterName)
        .engine(ENGINE)
        .engineVersion(ENGINE_VERSION)
        .port(MEMCACHED_PORT)
        .cacheNodeType(CACHE_TYPE)
        .numCacheNodes(config.getCacheNodeCount())
        .networkType(NETWORK_TYPE)
        .azMode(AZ_MODE)
        .preferredAvailabilityZones(config.getAvailabilityZones())
        .cacheSubnetGroupName(cfnSubnetGroup.getCacheSubnetGroupName())
        .vpcSecurityGroupIds(vpcSecurityGroupIds)
        .build();
  }

}
