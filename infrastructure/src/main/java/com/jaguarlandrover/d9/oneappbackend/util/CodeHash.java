/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.util;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class CodeHash {

  private CodeHash() {

  }

  /**
   * Searches the given directory for '.java' and 'pom.xml' files and computes the SHA-256 hash then base64 encodes
   * the result. This can be used as a means for detecting changes to files in-side of the directory (and subdirectories).
   *
   * @param pathDirectory A list of paths to a directory that contains '.java' and 'pom.xml' files.
   * @return A base64 encoded string of the SHA-265 of the relevant files.
   */
  public static String fileDirectoryJarAndPomSha256ToBase64(String... pathDirectory) {

    List<Path> paths = new ArrayList<>();
    for (String path : pathDirectory) {
      paths.addAll(listFiles(Paths.get(path)));
    }

    try {
      MessageDigest digester = MessageDigest.getInstance("SHA-256");
      for (Path p : paths) {
        byte[] data = Files.readAllBytes(p);
        digester.update(data);
      }
      return Base64.getEncoder().encodeToString(digester.digest());
    } catch (Exception ex) {
      throw new IllegalArgumentException("Could not compute Sha256 for file in path: " + pathDirectory);
    }
  }

  // list all.java and pom.xml  files from within a directory and subdirectories.
  private static List<Path> listFiles(Path path) {
    final List<Path> result;
    try (Stream<Path> walk = Files.walk(path)) {
      result = walk.filter(Files::isRegularFile).filter(file -> {
        String absPath = file.toAbsolutePath().toString();
        return absPath.endsWith("java") || absPath.endsWith("pom.xml");
      }).collect(Collectors.toList());
    } catch (Exception io) {
      throw new IllegalArgumentException("Could not list files with error: " + io.getMessage());
    }
    return result;
  }

}