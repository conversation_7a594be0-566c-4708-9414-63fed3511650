package com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.order;

import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.BaseEndpoint;
import java.util.HashMap;
import java.util.Map;
import software.amazon.awscdk.services.apigateway.Resource;

public class UnfinishedOrder extends BaseEndpoint {

  private final String methodBrand = method + queryParameter + "brand";
  private final String methodStatus = method + queryParameter + "status";

  private final String integrationBrand = integration + queryParameter + "brand";
  private final String integrationStatus = integration + queryParameter + "status";

  public UnfinishedOrder(Resource version, String targetUrl, String httpMethod) {
    super(version, targetUrl, httpMethod);
  }

  @Override
  protected Map<String, Boolean> initializeParamAreRequired() {
    Map<String, Boolean> map = new HashMap<>();
    map.put(methodBrand, true);
    map.put(methodStatus, true);
    return map;
  }

  @Override
  protected Map<String, String> initializeParamToBePassed() {
    Map<String, String> map = new HashMap<>();
    map.put(integrationStatus, methodStatus);
    map.put(integrationBrand, methodBrand);
    return map;
  }
}
