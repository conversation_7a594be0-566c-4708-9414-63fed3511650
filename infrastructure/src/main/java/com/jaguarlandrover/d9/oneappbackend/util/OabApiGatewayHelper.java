/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.util;

import static com.jaguarlandrover.d9.oneappbackend.api.OabApiGatewayEnvEnum.NOT_SUPPORT_IN_CN_REGION;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.APPLICATION_JSON_TYPE;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.HTTP_POST_METHOD;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.LAMBDA_CONFIG_MEMORY_SIZE;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.LAMBDA_CONFIG_RESERVED_CONCURRENT_EXECUTIONS;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.LAMBDA_CONFIG_TIMEOUT_IN_SECONDS;
import static software.amazon.awscdk.services.lambda.ApplicationLogLevel.INFO;
import static software.amazon.awscdk.services.lambda.LogFormat.JSON;

import com.jaguarlandrover.d9.oneappbackend.config.OabApiGatewayAppConfig;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import software.amazon.awscdk.Duration;
import software.amazon.awscdk.services.apigateway.ConnectionType;
import software.amazon.awscdk.services.apigateway.DomainNameOptions;
import software.amazon.awscdk.services.apigateway.HttpIntegration;
import software.amazon.awscdk.services.apigateway.IVpcLink;
import software.amazon.awscdk.services.apigateway.Integration;
import software.amazon.awscdk.services.apigateway.IntegrationOptions;
import software.amazon.awscdk.services.apigateway.IntegrationResponse;
import software.amazon.awscdk.services.apigateway.IntegrationType;
import software.amazon.awscdk.services.apigateway.LambdaIntegration;
import software.amazon.awscdk.services.apigateway.PassthroughBehavior;
import software.amazon.awscdk.services.apigateway.RestApi;
import software.amazon.awscdk.services.apigateway.VpcLink;
import software.amazon.awscdk.services.apigateway.VpcLinkProps;
import software.amazon.awscdk.services.certificatemanager.Certificate;
import software.amazon.awscdk.services.certificatemanager.ICertificate;
import software.amazon.awscdk.services.ec2.IVpc;
import software.amazon.awscdk.services.ec2.SecurityGroup;
import software.amazon.awscdk.services.ec2.SecurityGroupProps;
import software.amazon.awscdk.services.ec2.Vpc;
import software.amazon.awscdk.services.ec2.VpcAttributes;
import software.amazon.awscdk.services.ec2.VpcLookupOptions;
import software.amazon.awscdk.services.elasticloadbalancingv2.INetworkLoadBalancer;
import software.amazon.awscdk.services.elasticloadbalancingv2.NetworkLoadBalancer;
import software.amazon.awscdk.services.elasticloadbalancingv2.NetworkLoadBalancerAttributes;
import software.amazon.awscdk.services.iam.Effect;
import software.amazon.awscdk.services.iam.PolicyStatement;
import software.amazon.awscdk.services.iam.ServicePrincipal;
import software.amazon.awscdk.services.kms.IKey;
import software.amazon.awscdk.services.lambda.Code;
import software.amazon.awscdk.services.lambda.Function;
import software.amazon.awscdk.services.lambda.FunctionProps;
import software.amazon.awscdk.services.lambda.IFunction;
import software.amazon.awscdk.services.lambda.ILayerVersion;
import software.amazon.awscdk.services.lambda.LambdaInsightsVersion;
import software.amazon.awscdk.services.lambda.LayerVersion;
import software.amazon.awscdk.services.lambda.Runtime;
import software.amazon.awscdk.services.lambda.Tracing;
import software.amazon.awscdk.services.logs.LogGroup;
import software.amazon.awscdk.services.logs.LogGroupProps;
import software.amazon.awscdk.services.logs.RetentionDays;
import software.amazon.awscdk.services.route53.ARecord;
import software.amazon.awscdk.services.route53.HostedZoneAttributes;
import software.amazon.awscdk.services.route53.IHostedZone;
import software.amazon.awscdk.services.route53.IPublicHostedZone;
import software.amazon.awscdk.services.route53.PrivateHostedZone;
import software.amazon.awscdk.services.route53.PublicHostedZone;
import software.amazon.awscdk.services.route53.PublicHostedZoneAttributes;
import software.amazon.awscdk.services.route53.RecordTarget;
import software.amazon.awscdk.services.route53.targets.ApiGateway;
import software.amazon.awscdk.services.sqs.IQueue;
import software.constructs.Construct;



public class OabApiGatewayHelper {

  private OabApiGatewayHelper() {
  }

  /**
   * Get VPC from VPC ID.
   * @param scope scope
   * @param config configuration for API Gateway
   * @return VPC
   */
  public static IVpc getVpc(Construct scope, OabApiGatewayAppConfig config) {
    return Vpc.fromLookup(scope, config.getVpcName(), VpcLookupOptions.builder()
        .vpcId(config.getVpcId())
        .build());
  }

  public static IVpc getPrivateSubnetsOfVpc(Construct scope, OabApiGatewayAppConfig config) {
    return Vpc.fromVpcAttributes(scope, config.getVpcName() + "-private", getVpcAttributes(config));
  }

  private static VpcAttributes getVpcAttributes(OabApiGatewayAppConfig config) {
    return VpcAttributes.builder()
        .vpcId(config.getVpcId())
        .privateSubnetIds(config.getSubnetIds())
        .availabilityZones(config.getAvailabilityZones())
        .build();
  }

  /**
   * Get NLB by NLB ARN.
   * @param scope scope
   * @param nlbArn NLB ARN
   * @return NLB
   */
  public static INetworkLoadBalancer getNlb(Construct scope, String nlbArn) {
    return NetworkLoadBalancer.fromNetworkLoadBalancerAttributes(scope, "nlb",
        NetworkLoadBalancerAttributes.builder()
            .loadBalancerArn(nlbArn)
            .build());
  }

  /**
   * Create CloudWatch Log Group.
   */
  public static LogGroup createLogGroup(Construct scope, String logGroupName,
                                        String region, IKey encryptKeyLogs) {

    PolicyStatement statement = PolicyStatement.Builder.create()
        .effect(Effect.ALLOW)
        .principals(List.of(new ServicePrincipal("logs." + region + ".amazonaws.com")))
        .actions(List.of("kms:Encrypt*",
            "kms:Decrypt*",
            "kms:ReEncrypt*",
            "kms:GenerateDataKey*",
            "kms:Describe*"))
        .resources(List.of("*"))
        .build();
    encryptKeyLogs.addToResourcePolicy(statement);

    return new LogGroup(scope, logGroupName,
        LogGroupProps.builder()
            .encryptionKey(encryptKeyLogs)
            .logGroupName(logGroupName)
            .retention(RetentionDays.ONE_WEEK)
            .build());
  }

  /**
   * Create Security Group.
   * Allow all egress from lambda sg as advised by SRE
   * that there is a firewall checking all outgoing traffic.
   */
  public static SecurityGroup createSecurityGroup(Construct scope, IVpc vpc,
                                                  String sgName, String sgDesc) {
    return new SecurityGroup(scope, sgName,
        SecurityGroupProps.builder()
            .description(sgDesc)
            .allowAllOutbound(true)
            .securityGroupName(sgName)
            .vpc(vpc)
            .build()
    );
  }

  /**
   * Construct properties for Lambda functions.
   */
  public static Function createLambdaFunction(Construct scope,
                                              String functionName,
                                              String assetLocation,
                                              String handlerName,
                                              Runtime lambdaRuntime,
                                              IVpc lambdaVpc,
                                              SecurityGroup lambdaSg,
                                              IQueue deadLetterQueue,
                                              Map<String, String> environment,
                                              LogGroup logGroup,
                                              OabApiGatewayAppConfig config,
                                              OabDeploymentHelper deploymentHelper) {
    FunctionProps.Builder builder = FunctionProps.builder()
        .functionName(functionName)
        .code(Code.fromAsset(assetLocation))
        .handler(handlerName)
        .deadLetterQueue(deadLetterQueue)
        .vpc(lambdaVpc)
        .securityGroups(List.of(lambdaSg))
        .runtime(lambdaRuntime)
        .environment(environment)
        .timeout(Duration.seconds(LAMBDA_CONFIG_TIMEOUT_IN_SECONDS))
        .reservedConcurrentExecutions(LAMBDA_CONFIG_RESERVED_CONCURRENT_EXECUTIONS)
        .memorySize(LAMBDA_CONFIG_MEMORY_SIZE)
        .layers(createLayers(scope, config))
        .tracing(Tracing.DISABLED);

    // Some features are not supported by CN AWS region, such as SnapStart, InsightsVersion, LogGroup...
    if (deploymentHelper.isAvailable(NOT_SUPPORT_IN_CN_REGION)) {
      builder.insightsVersion(LambdaInsightsVersion.fromInsightVersionArn(config.getLambdaInsightsArn()))
          .applicationLogLevel(INFO.name())
          .logFormat(JSON.name())
          .logGroup(logGroup);
    }

    FunctionProps props = builder.build();

    return new Function(scope, functionName, props);
  }

  /**
   * Create a vpcLink.
   */
  public static VpcLink createVpcLink(Construct scope,
                                      INetworkLoadBalancer nlb,
                                      String id,
                                      String name,
                                      String description) {
    // VpcLink
    return new VpcLink(scope, id,
        VpcLinkProps.builder()
            .vpcLinkName(name)
            .description(description)
            .targets(List.of(nlb))
            .build());
  }

  /**
   * Create Request Integration with the target backend service.
   */
  public static Integration createIntegration(String httpMethod, String endpoint, IVpcLink vpcLink,
                                              Map<String, String> requestParameters) {
    try {
      return Integration.Builder.create()
          .type(IntegrationType.HTTP_PROXY)
          .integrationHttpMethod(httpMethod)
          .options(IntegrationOptions.builder()
              .connectionType(ConnectionType.VPC_LINK)
              .vpcLink(vpcLink)
              .requestParameters(requestParameters)
              .build())
          .uri(new URL(endpoint))
          .build();
    } catch (MalformedURLException e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * Create Request Integration with the target backend service that allows disabling http proxy and pass in method response.
   */
  public static Integration createIntegration(String httpMethod,
                                              String endpoint,
                                              IVpcLink vpcLink,
                                              Map<String, String> requestParameters,
                                              Optional<Boolean> httpProxy,
                                              List<IntegrationResponse> integrationResponse) {
    IntegrationType integrationType = httpProxy.orElse(true) ? IntegrationType.HTTP_PROXY : IntegrationType.HTTP;
    try {
      return Integration.Builder.create()
          .type(integrationType)
          .integrationHttpMethod(httpMethod)
          .options(IntegrationOptions.builder()
              .connectionType(ConnectionType.VPC_LINK)
              .vpcLink(vpcLink)
              .requestParameters(requestParameters)
              .integrationResponses(integrationResponse)
              .build())
          .uri(new URL(endpoint))
          .build();
    } catch (MalformedURLException e) {
      throw new RuntimeException(e);
    }
  }

  /**
   * Create Request Integration with the target backend service that allows disabling http proxy and customizing request/response mapping.
   */
  public static HttpIntegration createIntegration(String httpMethod, String endpoint, IVpcLink vpcLink,
                                                  Map<String, String> requestParameters, String requestTemplate,
                                                  String responseTemplate) {
    IntegrationOptions.Builder integrationOptionsBuilder = IntegrationOptions.builder()
            .connectionType(ConnectionType.VPC_LINK)
            .vpcLink(vpcLink)
            .passthroughBehavior(PassthroughBehavior.WHEN_NO_TEMPLATES);

    if (Objects.nonNull(requestParameters)) {
      integrationOptionsBuilder.requestParameters(requestParameters);
    }
    if (Objects.nonNull(requestTemplate)) {
      integrationOptionsBuilder.requestTemplates(Map.of(APPLICATION_JSON_TYPE, requestTemplate));
    }
    if (Objects.nonNull(responseTemplate)) {
      integrationOptionsBuilder.integrationResponses(getIntegrationResponses(responseTemplate));
    }

    return HttpIntegration.Builder.create(endpoint)
            .httpMethod(httpMethod)
            .proxy(false)
            .options(integrationOptionsBuilder.build())
            .build();
  }

  /**
   * Create HTTP Integration with retailer search endpoint.
   */
  public static HttpIntegration createHttpIntegrationForRetailer(OabApiGatewayAppConfig config) {
    String integrationEndpointUrl =
        config.getPlaceServiceUrl() + "/api/v1/retailer";
    String requestTemplate = """
            #set($inputRoot = $input.path('$'))
            {
                #set($searchLocation = $inputRoot.search_location)
                "searchLocation": {
                    #if($searchLocation.coordinates.lat != "")
                        "coordinates":{
                            "lat":$searchLocation.coordinates.lat,
                            "lng":$searchLocation.coordinates.lng
                        },
                         "distance":$inputRoot.search_location.distance
                    #else
                        "address":"$searchLocation.address",
                        "distance":$inputRoot.search_location.distance
                    #end
                }
                #set($searchFilters = $inputRoot.search_filters)
                #if($searchFilters != "")
                ,
                "searchFilters": {
                    #if($searchFilters.limit != "")
                    "limit":$searchFilters.limit
                        #if($searchFilters.service_types != "")
                        ,
                        #end
                    #end
                    #if($searchFilters.service_types != "")
                    "serviceTypes":$searchFilters.service_types
                    #end
                }
                #end
            }
            """;
    String responseTemplate = """
           #set($retailers = $input.path('$'))
            [
              #foreach($retailer in $retailers )
                {
                  "trading_title":"$retailer.tradingTitle",
                  "address_line":"$retailer.addressLine",
                  "address_line2":"$retailer.addressLine2",
                  "address_line3":"$retailer.addressLine3",
                  "locality":"$retailer.locality",
                  "county":"$retailer.county",
                  "postcode":"$retailer.postcode",
                  "main_phone":"$retailer.mainPhone",
                  "distance":"$retailer.distance",
                  "longitude":$retailer.longitude,
                  "latitude":$retailer.latitude,
                  "service_types":$retailer.serviceTypes
                }#if($foreach.hasNext),#end
              #end
            ]
           """;
    Map<String, String> requestTemplates = Map.of(APPLICATION_JSON_TYPE, requestTemplate);
    Map<String, String> headerParams = Map.of("integration.request.header.authorization","method.request.header.authorization");
    return HttpIntegration.Builder.create(integrationEndpointUrl)
        .httpMethod(HTTP_POST_METHOD)
        .proxy(false)
        .options(IntegrationOptions.builder()
            .requestTemplates(requestTemplates)
            .passthroughBehavior(PassthroughBehavior.WHEN_NO_TEMPLATES)
            .requestParameters(headerParams)
            .integrationResponses(getIntegrationResponses(responseTemplate))
            .build())
        .build();
  }

  private static List<IntegrationResponse> getIntegrationResponses(String template) {
    Map<String, String> responseTemplates = Map.of(APPLICATION_JSON_TYPE, template);
    return List.of(
            IntegrationResponse.builder()
                    .selectionPattern("200")
                    .statusCode("200")
                    .responseTemplates(responseTemplates)
                    .build(),
            IntegrationResponse.builder()
                    .selectionPattern("400")
                    .statusCode("400")
                    .build(),
            IntegrationResponse.builder()
                    .selectionPattern("401")
                    .statusCode("401")
                    .build(),
            IntegrationResponse.builder()
                    .selectionPattern("403")
                    .statusCode("403")
                    .build(),
            IntegrationResponse.builder()
                    .selectionPattern("404")
                    .statusCode("404")
                    .build(),
            IntegrationResponse.builder()
                    .selectionPattern("500")
                    .statusCode("500")
                    .build()
    );
  }

  /**
   * Create Lambda Integration.
   */
  public static Integration createLambdaIntegration(Construct scope, String functionName) {
    IFunction function = Function.fromFunctionName(scope, functionName, functionName);
    return LambdaIntegration.Builder.create(function)
        .build();

  }

  /**
   * Create HTTP Integration with public charging search endpoint.
   */
  public  static  HttpIntegration createHttpIntegrationForPublicCharging(OabApiGatewayAppConfig config, String httpMethod) {
    String integrationEndpointUrl =
        config.getPublicChargingServiceUrl() + "/api/v1/charging/public/stations";
    return HttpIntegration.Builder.create(integrationEndpointUrl)
        .httpMethod(httpMethod)
        .proxy(true)
        .build();
  }

  /**
   * Create a mapping to API Gateway custom domain name.
   */
  public static void createDomainName(Construct scope, OabApiGatewayAppConfig config,
                                      RestApi restApi) {
    String customName = config.getCustomName();
    String domain = config.getDomainName();
    String customDomainName = customName.concat(".").concat(domain);

    ICertificate certificate = Certificate.fromCertificateArn(scope, "acm-certificate",
        config.getCertificate());

    // Map the custom domain name to the API Gateway
    restApi.addDomainName("oab-domain-name", DomainNameOptions.builder()
        .domainName(customDomainName)
        .certificate(certificate)
        .build());

    // Define a custom domain name record in a Route53
    createAliasRecord(scope, config, customDomainName, restApi);

  }

  // DDA-104438 - create a 2nd customer hosted zone and record
  /**
   * Create a custom domain name version 2 for the API Gateway.
   */
  public static void createDomainNameV2(Construct scope, OabApiGatewayAppConfig config,
                                        RestApi restApi) {
    String customName = config.getCustomName();
    String domain = config.getDomainNameV2();
    if (domain.isEmpty()) {
      return;
    }
    String customDomainName = customName.concat(".").concat(domain);
    String certificateArn = config.getCertificateV2();
    if (certificateArn.isEmpty()) {
      return;
    }
    ICertificate certificate = Certificate.fromCertificateArn(scope, "acm-certificate2", certificateArn);

    restApi.addDomainName("oab-domain-nameV2", DomainNameOptions.builder()
        .domainName(customDomainName)
        .certificate(certificate)
        .build());

    // would prefer not to use this from the `createAliasRecord` block but would need to create a second block if not
    // This is temporary and V2 stuff will be moved to the normal yaml variables once old domain is no longer used
    // When externaldns is done we can also add the private hosted zone here
    // Define a custom domain name record in a Route53
    IPublicHostedZone publicHostedZone = PublicHostedZone.fromPublicHostedZoneAttributes(scope,
        "dev-jlr-hosted-zonev2", PublicHostedZoneAttributes.builder()
            .zoneName(customDomainName)
            .hostedZoneId(config.getHostedZoneIdV2())
            .build());

    // Define an Alias record that points to the API Gateway endpoint's custom domain name
    ARecord.Builder.create(scope, "oab-api-a-recordv2")
        .zone(publicHostedZone)
        .recordName(customDomainName)
        .target(RecordTarget.fromAlias(new ApiGateway(restApi)))
        .build();
  }

  /**
   * Create a Route53 record that points
   * to the API Gateway endpoint's custom domain name.
   */
  public static void createAliasRecord(Construct scope, OabApiGatewayAppConfig config,
                                       String customDomainName, RestApi restApi) {

    // Define a Route53 hosted zone
    IPublicHostedZone publicHostedZone = PublicHostedZone.fromPublicHostedZoneAttributes(scope,
        "dev-jlr-hosted-zone", PublicHostedZoneAttributes.builder()
            .zoneName(customDomainName)
            .hostedZoneId(config.getHostedZoneId())
            .build());

    IHostedZone privateHostedZone = PrivateHostedZone.fromHostedZoneAttributes(scope,
        "dev-jlr-private-hosted-zone", HostedZoneAttributes.builder()
            .zoneName(customDomainName)
            .hostedZoneId(config.getPrivateHostedZoneId())
            .build());

    // Define an Alias record that points to the API Gateway endpoint's custom domain name
    ARecord.Builder.create(scope, "oab-api-a-record")
        .zone(publicHostedZone)
        .recordName(customDomainName)
        .target(RecordTarget.fromAlias(new ApiGateway(restApi)))
        .build();

    ARecord.Builder.create(scope, "oab-api-private-a-record")
        .zone(privateHostedZone)
        .recordName(customDomainName)
        .target(RecordTarget.fromAlias(new ApiGateway(restApi)))
        .build();
  }

  private static List<ILayerVersion> createLayers(Construct scope, OabApiGatewayAppConfig config) {
    List<ILayerVersion> listOfLayers = new ArrayList<>();
    listOfLayers.add(LayerVersion.fromLayerVersionArn(scope, config.getDdTraceJava(), config.getDdTraceJavaVersion()));
    listOfLayers.add(LayerVersion.fromLayerVersionArn(scope, config.getDdExtension(), config.getDdExtensionVersion()));
    return listOfLayers;
  }

}
