package com.jaguarlandrover.d9.oneappbackend.util.resourcehelper;

import java.util.Map;
import lombok.Data;
import software.amazon.awscdk.services.apigateway.Resource;

@Data
public abstract class BaseEndpoint {

  public final String method = "method";
  public final String integration = "integration";

  public final String pathParameter = ".request.path.";
  public final String queryParameter = ".request.querystring.";
  public final String multiValueParameter = ".request.multivaluequerystring.";

  // Endpoint base info
  private Resource resource;
  private String targetUrl;
  private String httpMethod;

  // Endpoint parameters
  Map<String, String> paramToBePassed;
  Map<String, Boolean> paramAreRequired;

  /**
   * Base constructor for an endpoint.
   * */
  public BaseEndpoint(Resource resource, String targetUrl, String httpMethod) {
    this.resource = resource;
    this.targetUrl = targetUrl;
    this.httpMethod = httpMethod;

    this.paramAreRequired = initializeParamAreRequired();
    this.paramToBePassed = initializeParamToBePassed();
  }

  /**
   * initialize which param are required.
   * */
  protected abstract Map<String, Boolean> initializeParamAreRequired();

  /**
   * initialize which param to be passed.
   * */
  protected abstract Map<String, String> initializeParamToBePassed();
}
