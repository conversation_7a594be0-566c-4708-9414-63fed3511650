package com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.command;

import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.BaseEndpoint;
import java.util.HashMap;
import java.util.Map;
import software.amazon.awscdk.services.apigateway.Resource;

public class StopCharging extends BaseEndpoint {

  private final String methodSessionid = method + pathParameter + "sessionId";

  private final String integrationSessionid = integration + pathParameter + "sessionId";

  public StopCharging(Resource resource, String targetUrl, String httpMethod) {
    super(resource, targetUrl, httpMethod);
  }

  @Override
  protected Map<String, String> initializeParamToBePassed() {
    Map<String, String> map = new HashMap<>();
    map.put(integrationSessionid, methodSessionid);
    return map;
  }

  @Override
  protected Map<String, Boolean> initializeParamAreRequired() {
    Map<String, Boolean> map = new HashMap<>();
    map.put(methodSessionid, true);
    return map;
  }
}
