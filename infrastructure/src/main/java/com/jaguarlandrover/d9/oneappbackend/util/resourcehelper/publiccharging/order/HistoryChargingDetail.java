package com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.order;

import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.BaseEndpoint;
import java.util.HashMap;
import java.util.Map;
import software.amazon.awscdk.services.apigateway.Resource;

public class HistoryChargingDetail extends BaseEndpoint {

  private final String methodSessionId = method + pathParameter + "sessionId";
  private final String methodRetry = method + queryParameter + "retry";

  private final String integrationSessionId = integration + pathParameter + "sessionId";
  private final String integrationRetry = integration + queryParameter + "retry";

  public HistoryChargingDetail(Resource version, String targetUrl, String httpMethod) {
    super(version, targetUrl, httpMethod);
  }

  @Override
  protected Map<String, Boolean> initializeParamAreRequired() {
    Map<String, Boolean> map = new HashMap<>();
    map.put(methodSessionId, true);
    map.put(methodRetry, true);
    return map;
  }

  @Override
  protected Map<String, String> initializeParamToBePassed() {
    Map<String, String> map = new HashMap<>();
    map.put(integrationSessionId, methodSessionId);
    map.put(integrationRetry, methodRetry);
    return map;
  }
}
