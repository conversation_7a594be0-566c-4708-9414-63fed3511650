package com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.servicebooking.booking;

import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.BaseEndpoint;
import java.util.HashMap;
import java.util.Map;
import software.amazon.awscdk.services.apigateway.Resource;

public class BookingDetails extends BaseEndpoint {

  private final String methodBookingno = method + pathParameter + "bookingNo";

  private final String integrationBookingno = integration + pathParameter + "bookingNo";

  public BookingDetails(Resource resource, String targetUrl, String httpMethod) {
    super(resource, targetUrl, httpMethod);
  }

  @Override
  protected Map<String, String> initializeParamToBePassed() {
    Map<String, String> map = new HashMap<>();
    map.put(integrationBookingno, methodBookingno);
    return map;
  }

  @Override
  protected Map<String, Boolean> initializeParamAreRequired() {
    Map<String, Boolean> map = new HashMap<>();
    map.put(methodBookingno, true);
    return map;
  }
}
