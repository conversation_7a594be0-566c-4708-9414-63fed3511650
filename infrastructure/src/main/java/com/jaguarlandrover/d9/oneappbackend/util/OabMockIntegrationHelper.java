/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.util;

import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.APPLICATION_JSON_TYPE;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.HTTP_ANY_METHOD;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.NOT_FOUND_STATUS_CODE;

import java.util.List;
import java.util.Map;
import software.amazon.awscdk.services.apigateway.IntegrationResponse;
import software.amazon.awscdk.services.apigateway.MethodOptions;
import software.amazon.awscdk.services.apigateway.MethodResponse;
import software.amazon.awscdk.services.apigateway.MockIntegration;
import software.amazon.awscdk.services.apigateway.Model;
import software.amazon.awscdk.services.apigateway.PassthroughBehavior;
import software.amazon.awscdk.services.apigateway.Resource;


/**
 * Helper class for creating Mock Integration of AWS API Gateway.
 */
public final class OabMockIntegrationHelper {

  private OabMockIntegrationHelper() {
    throw new IllegalStateException("Utility class");
  }

  /**
   * Add Mock Integration which returns 404 status code for any other paths not defined in the API Gateway.
   * @param resource resource
   * @see <a href="https://jira.devops.jlr-apps.com/browse/MOB-18327">BE004- Configure API Gateway to handle unknown paths</a>
   */
  public static void addIntegrationForUndefinedPaths(Resource resource) {
    // Integration Response
    IntegrationResponse.Builder builder = IntegrationResponse.builder().statusCode(NOT_FOUND_STATUS_CODE);

    builder.responseTemplates(Map.of(
        APPLICATION_JSON_TYPE,
          """
            {
              "message": "User is not authorized to access this resource with an explicit deny or path is undefined"
            }
            """
    ));

    IntegrationResponse integrationResponse = builder.build();

    // Method Response
    List<MethodResponse> methodResponseList = List.of(MethodResponse.builder()
        .statusCode(NOT_FOUND_STATUS_CODE)
        .responseModels(Map.of(APPLICATION_JSON_TYPE, Model.EMPTY_MODEL))
        .build());

    // Add method to the resource
    resource.addMethod(HTTP_ANY_METHOD,
        MockIntegration.Builder.create()
            .integrationResponses(
              List.of(integrationResponse))
            .passthroughBehavior(PassthroughBehavior.NEVER)
            .requestTemplates(Map.of(
              APPLICATION_JSON_TYPE, String.format("{ \"statusCode\": %s }", NOT_FOUND_STATUS_CODE)))
            .build(),
        MethodOptions.builder()
            .authorizer(null)
            .requestParameters(null)
            .methodResponses(methodResponseList)
            .build()
    );

  }

}
