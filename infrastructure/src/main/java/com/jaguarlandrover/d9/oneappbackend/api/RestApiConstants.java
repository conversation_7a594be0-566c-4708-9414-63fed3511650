/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.api;

/**
 * Constants for API Gateway resources, methods and integration endpoints.
 */
public class RestApiConstants {
  // API Gateway
  public static final String APIGW_NAME = "one-app-backend-api-gateway";
  public static final String APIGW_DESC = "API Gateway to One App Backend microservices";
  public static final String APIGW_LOG_GROUP_NAME = "/aws/api-gateway/one-app-backend";

  // Authorizer
  public static final String APIGW_AUTHORIZER_NAME = APIGW_NAME + "-fr-authorizer";

  // Lambda Function Name
  public static final String LAMBDA_AUTHORIZER_FUNC_NAME = APIGW_AUTHORIZER_NAME + "-function";

  // Lambda Configurations
  public static final int LAMBDA_CONFIG_TIMEOUT_IN_SECONDS = 120;
  public static final int LAMBDA_CONFIG_RESERVED_CONCURRENT_EXECUTIONS = 20;
  public static final int LAMBDA_CONFIG_MEMORY_SIZE = 1024;

  // Log Group Name for Lambda Authorizer
  public static final String LAMBDA_AUTHORIZER_LOG_GROUP_NAME = "/custom/one-app-backend-fr-authorizer-function";

  // Resources / Paths
  public static final String API = "api";
  public static final String VERSION = "v1";
  public static final String ME_PATH = "me";
  public static final String AVATAR_PATH = "avatar";
  public static final String USERS_PATH = "users";

  public static final String CONSENTS_PATH = "consents";
  public static final String CONTRACTS_PATH = "contracts";

  public static final String VEHICLES_PATH = "vehicles";
  public static final String PREFERENCES_PATH = "preferences";
  public static final String VEHICLE_ID_PATH = "{vehicleId}";
  public static final String FEATURES_PATH = "features";
  public static final String TRIPS_PATH = "trips";
  public static final String TRIP_ID_PATH = "{tripId}";
  public static final String COMMANDS_PATH = "commands";
  public static final String COMMAND_PATH = "command";
  public static final String FAQ_PATH = "faqs";
  public static final String ADDRESSES_PATH = "addresses";
  public static final String ADDRESS_ID_PATH = "{addressId}";
  public static final String API_PROXY_PATH = "{proxy+}";
  public static final String CHARGING_PATH = "charging";
  public static final String SERVICE_BOOKING_PATH = "service-booking";
  public static final String BOOKINGS_PATH = "bookings";
  public static final String DEALERS_PATH = "dealers";
  public static final String DEALER_NUMBER_PATH = "{dealerNo}";
  public static final String BOOKING_NUMBER_PATH = "{bookingNo}";
  public static final String HOME_PATH = "home";
  public static final String CHARGE_PATH = "charge";
  public static final String CHARGERS_PATH = "chargers";
  public static final String CHARGER_ID_PATH = "{chargerId}";
  public static final String CHARGER_START = "start";
  public static final String CHARGER_STOP = "stop";
  public static final String CHARGER_STATUS = "status";
  public static final String CHARGER_SCHEDULES = "schedules";
  public static final String CHARGER_TARIFFS = "tariffs";
  public static final String CHARGER_HISTORY = "history";
  public static final String CHARGER_HISTORY_LAST = "last";
  public static final String CHARGER_HISTORY_CSV = "csv";
  public static final String CHARGER_SCHEDULES_DEFAULT = "default";
  public static final String RETAILERS_PATH = "retailers";
  public static final String RETAILERS_SERVICE_TYPES_PATH = "service-types";
  public static final String RETAILERS_CITIES_PATH = "cities";
  public static final String RETAILERS_CODE_PATH = "{tradingCode}";
  public static final String RETAILERS_FAVORITE_PATH = "favorite";
  public static final String EVENT_PATH = "event";
  public static final String PUBLIC_PATH = "public";
  public static final String STATIONS_PATH = "stations";
  public static final String STATION_ID_PATH = "{stationId}";
  public static final String TRENDING_KEYWORDS = "trending-keywords";
  public static final String FILTERS = "filters";
  public static final String FILTER = "{filter}";
  public static final String FAQS = "faqs";
  public static final String CONNECTOR_DETAIL = "connector-detail";
  public static final String CHARGE_SESSIONS = "sessions";
  public static final String CHARGE_SESSION_ID = "{sessionId}";
  public static final String CHARGE_HISTORY = "charge-history";
  public static final String CHARGE_OCCUPANCY = "occupancy";

  public static final String PAAK_PATH = "paak";
  public static final String PAIRING_PATH = "pairing";
  public static final String PHYSICAL_PATH = "physical";
  public static final String KEYS_PATH = "keys";
  public static final String SLOT_ID_PATH = "{slotId}";
  public static final String SUSPEND_PATH = "suspend";
  public static final String RESUME_PATH = "resume";
  public static final String DEVICES = "devices";
  public static final String IDENTITY_MAPPING = "identity-mapping";

  public static final String SCHEDULES_PATH = "schedules";
  public static final String DEPARTURES_PATH = "departures";
  public static final String ROUTINES_PATH = "routines";
  public static final String OFF_PEAK_PATH = "off-peak";
  public static final String SMART_PATH = "smart";
  public static final String VEHICLE_POSITION_PATH = "positions";
  public static final String VEHICLE_POSITION_LATEST_PATH = "latest";
  public static final String VEHICLE_POSITION_PARKED_PATH = "parked";
  public static final String FEEDBACK_PATH = "feedback";
  public static final String FORM_PATH = "form";
  public static final String FORM_ID_PATH = "{formId}";
  public static final String LANGUAGE_PATH = "{language}";
  public static final String VERIFICATIONS_PATH = "verifications";
  public static final String GUARDIAN_MODE_PATH = "guardian-mode";
  public static final String GEOFENCE_SERVICE_PATH = "geofence";
  public static final String GUARDIAN_MODE_SCHEDULE_PATH = "schedule";
  public static final String ID_PATH = "{id}";
  public static final String DEVICE_ID_PATH = "{device_id}";

  //[CN]Add Vehicle for current user
  public static final String VEHICLE_RECOGNIZE_PATH = "recognize";
  public static final String VEHICLE_CONFIRM_PATH = "confirm";

  // HTTP Methods
  public static final String HTTP_ANY_METHOD = "ANY";
  public static final String HTTP_GET_METHOD = "GET";
  public static final String HTTP_PUT_METHOD = "PUT";
  public static final String HTTP_POST_METHOD = "POST";
  public static final String HTTP_PATCH_METHOD = "PATCH";
  public static final String HTTP_DELETE_METHOD = "DELETE";

  // Content Types
  public static final String APPLICATION_JSON_TYPE = "application/json";
  public static final String MULTIPART_FORM_DATA = "multipart/form-data";

  // Status Codes
  public static final String SUCCESS_STATUS_CODE = "200";
  public static final String NOT_FOUND_STATUS_CODE = "404";

  // Datadog Unified Tags
  public static final String DD_ENV_TAG = "env";
  public static final String DD_SERVICE_TAG = "service";
  public static final String DD_VERSION_TAG = "version";

  // Datadog Additional tags
  public static final String DD_MANAGED_BY_TAG = "managed_by";
  public static final String DD_PII_DATA_HANDLER_TAG = "pii_data_handler";
  public static final String DD_PRODUCT_OWNER_TAG = "product_owner";
  public static final String DD_REPO_URL_TAG = "repo_url";
  public static final String DD_SQUAD_TAG = "squad";

  // WAFPolicy Tag only for CN: it will bind WAF for gateway API when set as "Core"
  public static final String WAF_POLICY_TAG = "WAFPolicy";
}
