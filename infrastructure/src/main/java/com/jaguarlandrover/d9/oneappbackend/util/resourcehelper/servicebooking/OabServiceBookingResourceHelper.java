package com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.servicebooking;

import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.BOOKINGS_PATH;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.BOOKING_NUMBER_PATH;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.DEALERS_PATH;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.DEALER_NUMBER_PATH;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.HTTP_DELETE_METHOD;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.HTTP_POST_METHOD;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.SERVICE_BOOKING_PATH;

import com.jaguarlandrover.d9.oneappbackend.config.OabApiGatewayAppConfig;
import com.jaguarlandrover.d9.oneappbackend.util.OabDeploymentHelper;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.BaseEndpoint;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.servicebooking.booking.BookingCancel;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.servicebooking.booking.BookingDetails;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.servicebooking.booking.BookingList;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.servicebooking.booking.BookingSubmit;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.servicebooking.booking.UploadFiles;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.servicebooking.dealer.DealerAdvisors;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.servicebooking.dealer.DealerLastOne;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.servicebooking.dealer.DealerSchedule;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import software.amazon.awscdk.services.apigateway.Resource;

@Getter
public class OabServiceBookingResourceHelper {

  private final OabDeploymentHelper deploymentHelper;
  private final OabApiGatewayAppConfig config;

  private final List<BaseEndpoint> endpoints;

  /**
   * Constructor for class OabPublicChargingResourceHelper. Create resource.
   * @param deploymentHelper OabDeploymentHelper class
   * @param version API version
   * @param config Public charging service url
   */
  public OabServiceBookingResourceHelper(OabDeploymentHelper deploymentHelper, Resource version, OabApiGatewayAppConfig config) {
    // set-up env
    this.deploymentHelper = deploymentHelper;
    this.config = config;
    this.endpoints = new ArrayList<>();

    registerEndpoints(version);
  }

  /**
   * Resource only has one instance, duplicate resource will cause error.
   * so we have to create all endpoints here.
   * @param version API version 'api/v1/'
   * */
  private void registerEndpoints(Resource version) {
    // Booking Base Url api/v1/service-booking
    Resource serviceBooking = version.addResource(SERVICE_BOOKING_PATH);

    // api/v1/service-booking/bookings
    Resource rebooking = serviceBooking.addResource(BOOKINGS_PATH);
    // api/v1/service-booking/bookings/{bookingNo}
    Resource reCancelBooking = rebooking.addResource(BOOKING_NUMBER_PATH);
    // api/v1/service-booking/bookings/query
    Resource rebookingList = rebooking.addResource("query");
    // api/v1/service-booking/bookings/upload
    Resource reUpload = rebooking.addResource("upload");

    // api/v1/service-booking/dealers
    Resource reDealers = serviceBooking.addResource(DEALERS_PATH);
    // api/v1/service-booking/dealers/{dealerNo}
    Resource reDealerNo = reDealers.addResource(DEALER_NUMBER_PATH);
    // api/v1/service-booking/dealers/{dealerNo}/advisors
    Resource reAdvisors = reDealerNo.addResource("advisors");
    // api/v1/service-booking/dealers/{dealerNo}/schedules
    Resource reSchedules = reDealerNo.addResource("schedules");
    // api/v1/service-booking/dealers/last
    Resource reLastOne = reDealers.addResource("last");

    // Service-Booking endpoints initialization
    BookingCancel bookingCancel = new BookingCancel(reCancelBooking, config.getServiceBookingCancelUrl(), HTTP_DELETE_METHOD);
    BookingList bookingList = new BookingList(rebookingList, config.getServiceBookingListUrl(), HTTP_POST_METHOD);
    BookingDetails bookingDetails = new BookingDetails(reCancelBooking, config.getServiceBookingDetailsUrl(), HTTP_POST_METHOD);
    BookingSubmit bookingSubmit = new BookingSubmit(rebooking, config.getServiceBookingSubmitUrl(), HTTP_POST_METHOD);
    UploadFiles uploadFiles = new UploadFiles(reUpload, config.getServiceBookingFileUploadUrl(), HTTP_POST_METHOD);
    DealerAdvisors dealerAdvisors = new DealerAdvisors(reAdvisors, config.getServiceBookingServiceAdvisorsUrl(), HTTP_POST_METHOD);
    DealerLastOne dealerLastOne = new DealerLastOne(reLastOne, config.getServiceBookingLastDealerUrl(), HTTP_POST_METHOD);
    DealerSchedule dealerSchedule = new DealerSchedule(reSchedules, config.getServiceBookingDealerScheduleUrl(), HTTP_POST_METHOD);

    endpoints.add(bookingDetails);
    endpoints.add(bookingCancel);
    endpoints.add(bookingList);
    endpoints.add(bookingSubmit);
    endpoints.add(uploadFiles);
    endpoints.add(dealerAdvisors);
    endpoints.add(dealerLastOne);
    endpoints.add(dealerSchedule);
  }
}
