/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.jaguarlandrover.d9.oneappbackend.api.OabApiGatewayEnvEnum;
import com.jaguarlandrover.d9.oneappbackend.config.OabApiGatewayEnvConfig;
import java.io.File;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.apache.logging.log4j.Logger;

public class OabDeploymentHelper {
  private static final String FILEPATH = "toggle/oab-deployment.yaml";
  private final OabApiGatewayEnvConfig envConfig;
  private final String deploymentStage;
  private static final Logger LOGGER = LogUtility.getInstance();

  public OabDeploymentHelper(String deploymentStage) {
    envConfig = loadEnvConfiguration();
    this.deploymentStage = deploymentStage;
  }

  /**
   * Load configurations from YAML file.
   * @return configuration from YAML file
   */
  private OabApiGatewayEnvConfig loadEnvConfiguration() {
    File file = new File(Thread.currentThread().getContextClassLoader()
        .getResource(FILEPATH).getFile());

    // Instantiating a new ObjectMapper as a YAMLFactory
    ObjectMapper om = new ObjectMapper(new YAMLFactory());

    OabApiGatewayEnvConfig oabApiGatewayEnvConfig;
    try {
      LOGGER.info("Env toggle of deploymentStage file read.");
      oabApiGatewayEnvConfig = om.readValue(file, OabApiGatewayEnvConfig.class);
    } catch (IOException e) {
      LOGGER.error(e.getMessage(), e);
      oabApiGatewayEnvConfig = new OabApiGatewayEnvConfig();
    }
    return oabApiGatewayEnvConfig;
  }

  /**
   * Determine if the current environment is available.
   * @return the feature is available or not
   */
  public boolean isAvailable(OabApiGatewayEnvEnum envEnum) {
    if (this.envConfig.getToggle().containsKey(envEnum.getName())) {
      List<String> list = this.envConfig.getToggle().get(envEnum.getName());
      Set<String> availableSet = new HashSet<>(list);
      return availableSet.contains(this.deploymentStage);
    }
    return false;
  }
}
