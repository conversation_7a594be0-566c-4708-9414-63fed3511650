package com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.servicebooking.dealer;

import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.BaseEndpoint;
import java.util.HashMap;
import java.util.Map;
import software.amazon.awscdk.services.apigateway.Resource;

public class DealerAdvisors extends BaseEndpoint {

  private final String methodDealerNo = method + pathParameter + "dealerNo";

  private final String integrationDealerNo = integration + pathParameter + "dealerNo";

  public DealerAdvisors(Resource resource, String targetUrl, String httpMethod) {
    super(resource, targetUrl, httpMethod);
  }

  @Override
  protected Map<String, String> initializeParamToBePassed() {
    Map<String, String> map = new HashMap<>();
    map.put(integrationDealerNo, methodDealerNo);
    return map;
  }

  @Override
  protected Map<String, Boolean> initializeParamAreRequired() {
    Map<String, Boolean> map = new HashMap<>();
    map.put(methodDealerNo, true);
    return map;
  }
}
