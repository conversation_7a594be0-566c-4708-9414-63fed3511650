package com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.servicebooking.booking;

import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.BaseEndpoint;
import java.util.HashMap;
import java.util.Map;
import software.amazon.awscdk.services.apigateway.Resource;

public class BookingCancel extends BaseEndpoint {

  public BookingCancel(Resource resource, String targetUrl, String httpMethod) {
    super(resource, targetUrl, httpMethod);
  }

  @Override
  protected Map<String, String> initializeParamToBePassed() {
    Map<String, String> map = new HashMap<>();
    map.put(integrationBookingNo, methodBookingNo);
    return map;
  }

  @Override
  protected Map<String, Boolean> initializeParamAreRequired() {
    Map<String, Boolean> map = new HashMap<>();
    map.put(methodBookingNo, true);
    map.put(methodTakeToSendNo, false);
    map.put(methodOid, false);
    map.put(methodReasonType, true);
    map.put(methodReason, true);
    map.put(methodName, true);
    map.put(methodDataSource, true);
    return map;
  }

  private final String methodBookingNo = method + pathParameter + "bookingNo";
  private final String methodTakeToSendNo = method + queryParameter + "takeToSendNo";
  private final String methodOid = method + queryParameter + "oid";
  private final String methodReasonType = method + queryParameter + "reasonType";
  private final String methodReason = method + queryParameter + "reason";
  private final String methodName = method + queryParameter + "name";
  private final String methodDataSource = method + queryParameter + "dataSource";

  private final String integrationBookingNo = integration + pathParameter + "bookingNo";
}
