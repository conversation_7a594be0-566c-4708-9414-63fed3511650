/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.util;

public class OabGuardianModeResourceHelper {

  private OabGuardianModeResourceHelper() {
  }

  public static final String GET_SCHEDULE_RESPONSE_TEMPLATE = """
          #set($schedules = $input.path('$'))
          [
            #foreach($schedule in $schedules)
            {
                "id": "$schedule.id",
                "vehicle_id": "$schedule.vuid",
                "status": "$schedule.status",
                "start_date_time": "$schedule.startDateTime",
                "end_date_time": "$schedule.endDateTime"
            }#if($foreach.hasNext),#end
            #end
          ]
          """;
  public static final String POST_SCHEDULE_REQUEST_TEMPLATE = """
          #set($schedule = $input.path('$'))
          {
              "vuid": "$schedule.vehicle_id",
              "startDateTime": "$schedule.start_date_time",
              "endDateTime": "$schedule.end_date_time"
          }
          """;
  public static final String POST_SCHEDULE_RESPONSE_TEMPLATE = """
          #set($schedule = $input.path('$'))
          {
              "id": "$schedule.id",
              "vehicle_id": "$schedule.vuid",
              "status": "$schedule.status",
              "start_date_time": "$schedule.startDateTime",
              "end_date_time": "$schedule.endDateTime"
          }
          """;
  public static final String PUT_SCHEDULE_REQUEST_TEMPLATE = """
          #set($schedule = $input.path('$'))
          {
              #if($schedule.start_date_time)
              "startDateTime": "$schedule.start_date_time",
              #end
              #if($schedule.end_date_time)
              "endDateTime": "$schedule.end_date_time"
              #end
          }
          """;
  public static final String PUT_SCHEDULE_RESPONSE_TEMPLATE = """
          #set($schedule = $input.path('$'))
          {
              "id": "$schedule.id",
              "vehicle_id": "$schedule.vuid",
              "status": "$schedule.status",
              "start_date_time": "$schedule.startDateTime",
              "end_date_time": "$schedule.endDateTime"
          }
          """;
}
