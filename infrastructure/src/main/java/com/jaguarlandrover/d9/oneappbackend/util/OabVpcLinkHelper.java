/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.util;

import java.util.List;
import software.amazon.awscdk.Duration;
import software.amazon.awscdk.services.apigateway.VpcLink;
import software.amazon.awscdk.services.apigateway.VpcLinkProps;
import software.amazon.awscdk.services.ec2.IVpc;
import software.amazon.awscdk.services.ec2.SecurityGroup;
import software.amazon.awscdk.services.ec2.SubnetSelection;
import software.amazon.awscdk.services.elasticloadbalancingv2.BaseNetworkListenerProps;
import software.amazon.awscdk.services.elasticloadbalancingv2.HealthCheck;
import software.amazon.awscdk.services.elasticloadbalancingv2.INetworkLoadBalancer;
import software.amazon.awscdk.services.elasticloadbalancingv2.INetworkLoadBalancerTarget;
import software.amazon.awscdk.services.elasticloadbalancingv2.NetworkListenerAction;
import software.amazon.awscdk.services.elasticloadbalancingv2.NetworkLoadBalancer;
import software.amazon.awscdk.services.elasticloadbalancingv2.NetworkLoadBalancerProps;
import software.amazon.awscdk.services.elasticloadbalancingv2.NetworkTargetGroup;
import software.amazon.awscdk.services.elasticloadbalancingv2.NetworkTargetGroupProps;
import software.amazon.awscdk.services.lambda.Code;
import software.amazon.awscdk.services.lambda.Runtime;
import software.amazon.awscdk.services.lambda.SingletonFunction;
import software.amazon.awscdk.services.lambda.SingletonFunctionProps;
import software.amazon.awscdk.services.logs.RetentionDays;
import software.constructs.Construct;


public class OabVpcLinkHelper {

  /**
   * Create Target Group.
   */
  public static NetworkTargetGroup createTargetGroup(Construct scope,
                                                     String targetPrefix,
                                                     IVpc lambdaVpc,
                                                     List<INetworkLoadBalancerTarget> targets) {
    String targetGroupName = String.format("%s-endpoint-target", targetPrefix);
    return new NetworkTargetGroup(scope, targetGroupName,
            NetworkTargetGroupProps.builder()
                .targetGroupName(targetGroupName)
                .port(443)
                .targets(targets)
                .deregistrationDelay(Duration.seconds(10))
                .vpc(lambdaVpc)
                .healthCheck(HealthCheck.builder()
                    .interval(Duration.seconds(15))
                    .timeout(Duration.seconds(2))
                    .build())
                .build());
  }

  /**
   * Create a vpcLink By NLB.
   */
  public static VpcLink createVpcLink(Construct scope,
                                      INetworkLoadBalancer nlb,
                                      String id,
                                      String name,
                                      String description) {
    // VpcLink
    return new VpcLink(scope, id,
        VpcLinkProps.builder()
            .vpcLinkName(name)
            .description(description)
            .targets(List.of(nlb))
            .build());
  }

  /**
   * Create VPC Link.
   */
  public static VpcLink createVpcLink(Construct scope,
                                      String vpcLinkPrefix,
                                      IVpc lambdaVpc,
                                      SubnetSelection subnetSelection,
                                      NetworkTargetGroup targetGroup) {

    // NLB for VPC endpoint
    NetworkLoadBalancer nlb =
        new NetworkLoadBalancer(scope, String.format("%s-endpoint-alb", vpcLinkPrefix),
            NetworkLoadBalancerProps.builder()
                .loadBalancerName(String.format("%s-endpoint-alb", vpcLinkPrefix))
                .vpc(lambdaVpc)
                .internetFacing(false)
                .vpcSubnets(subnetSelection)
                .build());
    nlb.addListener(String.format("%s-endpoint-listener", vpcLinkPrefix),
        BaseNetworkListenerProps.builder()
            .port(80)
            .defaultAction(NetworkListenerAction.forward(List.of(targetGroup)))
            .build());
    nlb.addListener(String.format("%s-endpoint-https-listener", vpcLinkPrefix),
        BaseNetworkListenerProps.builder()
            .port(443)
            .defaultAction(NetworkListenerAction.forward(List.of(targetGroup)))
            .build());

    String vpcLinkName = String.format("%s-endpoint-vpc-link", vpcLinkPrefix);
    return createVpcLink(scope,
        nlb,
        vpcLinkName,
        vpcLinkName,
        String.format("VPC Link for connection to %s", vpcLinkPrefix));
  }

  /**
   * Create Lambda for ENI Custom Resource.
   */
  public static SingletonFunction createEniCustomResourceLambda(
      Construct scope,
      IVpc lambdaVpc,
      SecurityGroup lambdaSg) {

    return new SingletonFunction(scope, "EniCustomResourceFunction",
        SingletonFunctionProps.builder()
            .handler("com.jaguarlandrover.d9.oneappbackend.lambda.EniIpLookup")
            .code(Code.fromAsset(
                "../software/eniiplookup/target/eni-ip-lookup-1.0.jar"))
            .runtime(Runtime.JAVA_17)
            .memorySize(512)
            .vpc(lambdaVpc)
            .securityGroups(List.of(lambdaSg))
            .logRetention(RetentionDays.ONE_WEEK)
            .timeout(Duration.seconds(15))
            .uuid("03fe7b53-78b5-4b23-a034-28ae79536da7")
            .build());
  }
}
