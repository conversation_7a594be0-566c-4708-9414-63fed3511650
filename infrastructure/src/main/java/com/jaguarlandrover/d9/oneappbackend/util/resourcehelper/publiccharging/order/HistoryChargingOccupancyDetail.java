package com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.order;

import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.BaseEndpoint;
import java.util.HashMap;
import java.util.Map;
import software.amazon.awscdk.services.apigateway.Resource;

public class HistoryChargingOccupancyDetail extends BaseEndpoint {

  private final String methodSessionId = method + pathParameter + "sessionId";

  private final String integrationSessionId = integration + pathParameter + "sessionId";

  public HistoryChargingOccupancyDetail(Resource version, String targetUrl, String httpMethod) {
    super(version, targetUrl, httpMethod);
  }

  @Override
  protected Map<String, Boolean> initializeParamAreRequired() {
    Map<String, Boolean> map = new HashMap<>();
    map.put(methodSessionId, true);
    return map;
  }

  @Override
  protected Map<String, String> initializeParamToBePassed() {
    Map<String, String> map = new HashMap<>();
    map.put(integrationSessionId, methodSessionId);
    return map;
  }
}
