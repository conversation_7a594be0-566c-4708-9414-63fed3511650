package com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.search;

import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.BaseEndpoint;
import java.util.HashMap;
import java.util.Map;
import software.amazon.awscdk.services.apigateway.Resource;

public class ConnectorDetail extends BaseEndpoint {

  private final String methodStationid = method + pathParameter + "stationId";

  private final String integrationStationid = integration + pathParameter + "stationId";

  public ConnectorDetail(Resource resource, String targetUrl, String httpMethod) {
    super(resource, targetUrl, httpMethod);
  }

  @Override
  protected Map<String, String> initializeParamToBePassed() {
    Map<String, String> map = new HashMap<>();
    map.put(integrationStationid, methodStationid);
    return map;
  }

  @Override
  protected Map<String, Boolean> initializeParamAreRequired() {
    Map<String, Boolean> map = new HashMap<>();
    map.put(methodStationid, true);
    return map;
  }
}
