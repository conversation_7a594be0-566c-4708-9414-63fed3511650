package com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.search;

import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.BaseEndpoint;
import java.util.Map;
import software.amazon.awscdk.services.apigateway.Resource;

public class Faqs extends BaseEndpoint {
  public Faqs(Resource version, String targetUrl, String httpMethod) {
    super(version, targetUrl, httpMethod);
  }

  @Override
  protected Map<String, Boolean> initializeParamAreRequired() {
    return null;
  }

  @Override
  protected Map<String, String> initializeParamToBePassed() {
    return null;
  }
}
