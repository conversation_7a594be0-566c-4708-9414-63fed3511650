/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.util;

import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.DD_ENV_TAG;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.DD_MANAGED_BY_TAG;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.DD_PII_DATA_HANDLER_TAG;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.DD_PRODUCT_OWNER_TAG;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.DD_REPO_URL_TAG;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.DD_SERVICE_TAG;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.DD_SQUAD_TAG;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.DD_VERSION_TAG;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import software.amazon.awscdk.IResource;
import software.amazon.awscdk.TagProps;
import software.amazon.awscdk.Tags;
import software.constructs.Construct;

public class OabDatadogHelper {

  private static final String DD_VERSION_FORMAT = "yyyy-MM-dd-Hm";

  /**
   * Generate version number for Datadog Version Tag.
   * @return build version number for Datadog
   */
  public static String generateDdVersion() {
    return new SimpleDateFormat(DD_VERSION_FORMAT).format(new Date());
  }


  /**
   * Add the Datadog unified tags to all resources in the given stack.
   *
   * @param construct        stack
   * @param env              value for datadog environment tag
   * @param service          value for datadog service tag
   * @param version          value for datadog version tag
   * @param functionService  value for datadog service tag for lambda function
   * @param managedBy        value for datadog managedBy tag
   * @param piiDataHandler   value for datadog piiDataHandler tag
   * @param productOwner     value for datadog productOwner tag
   * @param repoUrl          value for datadog repoUrl tag
   * @param squad            value for datadog squad tag
   */
  public static void prepareDataDogTagsByStack(Construct construct,
                                               String env,
                                               String service,
                                               String version,
                                               String functionService,
                                               String managedBy,
                                               String piiDataHandler,
                                               String productOwner,
                                               String repoUrl,
                                               String squad) {
    Tags.of(construct).add(DD_ENV_TAG, env);

    Tags.of(construct).add(DD_SERVICE_TAG, service, TagProps.builder()
        .applyToLaunchedInstances(false)
        .excludeResourceTypes(Arrays.asList("AWS::Lambda::Function"))
        .build());
    Tags.of(construct).add(DD_SERVICE_TAG, functionService, TagProps.builder()
        .applyToLaunchedInstances(false)
        .includeResourceTypes(Arrays.asList("AWS::Lambda::Function"))
        .build());

    Tags.of(construct).add(DD_VERSION_TAG, version);

    Tags.of(construct).add(DD_MANAGED_BY_TAG, managedBy);
    Tags.of(construct).add(DD_PII_DATA_HANDLER_TAG, piiDataHandler);
    Tags.of(construct).add(DD_PRODUCT_OWNER_TAG, productOwner);
    Tags.of(construct).add(DD_REPO_URL_TAG, repoUrl);
    Tags.of(construct).add(DD_SQUAD_TAG, squad);
  }

  /**
   * Add the Datadog unified tags to given resources.
   *
   * @param resource         resource to which datadog unified tags are added
   * @param env              value for datadog environment tag
   * @param service          value for datadog service tag
   * @param version          value for datadog version tag
   * @param managedBy        value for datadog managedBy tag
   * @param piiDataHandler   value for datadog piiDataHandler tag
   * @param productOwner     value for datadog productOwner tag
   * @param repoUrl          value for datadog repoUrl tag
   * @param squad            value for datadog squad tag
   */
  public static void prepareDataDogTagsByResource(IResource resource,
                                                  String env,
                                                  String service,
                                                  String version,
                                                  String managedBy,
                                                  String piiDataHandler,
                                                  String productOwner,
                                                  String repoUrl,
                                                  String squad) {
    Tags.of(resource).add(DD_ENV_TAG, env);
    Tags.of(resource).add(DD_SERVICE_TAG, service);
    Tags.of(resource).add(DD_VERSION_TAG, version);
    Tags.of(resource).add(DD_MANAGED_BY_TAG, managedBy);
    Tags.of(resource).add(DD_PII_DATA_HANDLER_TAG, piiDataHandler);
    Tags.of(resource).add(DD_PRODUCT_OWNER_TAG, productOwner);
    Tags.of(resource).add(DD_REPO_URL_TAG, repoUrl);
    Tags.of(resource).add(DD_SQUAD_TAG, squad);
  }

}
