/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend;

import com.jaguarlandrover.d9.oneappbackend.config.OabApiGatewayAppConfig;
import com.jaguarlandrover.d9.oneappbackend.config.OabApiGatewaySystemConfig;
import com.jaguarlandrover.d9.oneappbackend.util.AppConfigLoader;
import com.jaguarlandrover.d9.oneappbackend.util.OabDatadogHelper;
import java.io.FileNotFoundException;
import java.util.Arrays;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awscdk.App;
import software.amazon.awscdk.Environment;
import software.amazon.awscdk.StackProps;


@Slf4j
public class OabApiGatewayApp {

  private static final String API_GATEWAY_STACK_NAME = "oab-api-gateway-stack";

  /**
   * Create and synthesize CDK stacks.
   */
  public static void main(final String[] args) {
    App app = new App();

    final String configEnv = System.getenv("CI_ENVIRONMENT_NAME");

    try {
      // Application Configurations from YML file
      OabApiGatewayAppConfig appConfig = AppConfigLoader.load(configEnv);

      // System Configurations
      final String ecoIntVpceIps = System.getenv("ECO_INT_VPCE_IPS");
      OabApiGatewaySystemConfig systemConfig = OabApiGatewaySystemConfig.builder()
          .ddVersion(OabDatadogHelper.generateDdVersion())
          .ecoIntVpceIps(Arrays.stream(ecoIntVpceIps.split(":")).toList())
          .build();

      Environment env = Environment.builder()
          .account(appConfig.getAccount())
          .region(appConfig.getRegion())
          .build();

      new OabApiGatewayStack(app,
        API_GATEWAY_STACK_NAME,
        StackProps.builder()
          .env(env)
          .build(),
        appConfig,
        systemConfig);

      app.synth();
    } catch (FileNotFoundException e) {
      log.error("No configuration file found. Failed to synth application.", e);
    }
  }

}
