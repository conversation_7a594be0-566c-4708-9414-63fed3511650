/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.config;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * Environmental configurations for One App Backend CloudFormation App.
 * Values are loaded from /src/main/resources/config/${env}.yaml
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OabApiGatewayAppConfig {

  private String vpcName;
  private String vpcId;
  private List<String> subnetIds;
  private String region;
  private String account;
  private List<String> availabilityZones;
  private String deploymentStage;
  private String forgeRockHost;
  private String policyArnFormat;
  private String approovEnabled;
  private String approovSecretArn;
  private String certificate;
  private String customName;
  private String domainName;
  private String hostedZoneId;
  private String privateHostedZoneId;
  private String commandRouterUrl;
  private Integer cacheNodeCount;
  private String placeServiceUrl;
  private String scheduleServiceUrl;
  private String guardianModeServiceScheduleEndpointUrl;
  private String guardianModeServiceScheduleByIdEndpointUrl;
  private String geofenceServiceEndpointUrl;

  // VPC Links
  private String nginxNlbArn;
  private String apigwVpcEndpoint;

  // Datadog environment tag
  private String ddEnv;
  // Datadog service tag
  private String ddService;
  // Datadog service tag for ForgeRock Lambda Authorizer
  private String ddServiceLambdaAuthorizer;

  // Datadog additional tags
  private String ddManagedBy;
  private String ddPiiDataHandler;
  private String ddProductOwner;
  private String ddSquad;
  private String ddRepoUrl;

  // Datadog lambda instrumentation variables
  private String ddWrapper;
  private String ddSite;
  private String ddSecretArn;

  // Datadog trace and extension arn version
  private String ddExtension;
  private String ddExtensionVersion;
  private String ddTraceJava;
  private String ddTraceJavaVersion;
  private String lambdaInsightsArn;

  // Endpoints to EKS Services
  private String userServiceUsersEndpointUrl;
  private String userServiceUsersRegisterEndpointUrl;
  private String userServiceUsersUpdateEndpointUrl;
  private String userServiceUsersDeleteEndpointUrl;
  private String userServiceUsersAvatarEndpointUrl;
  private String userServiceUsersRetrieveConsentsEndpointUrl;
  private String userServiceUsersUpdateConsentsEndpointUrl;
  private String featureServiceMeEndpointUrl;
  private String vehicleServiceMyVehiclesEndpointUrl;
  private String preferenceServiceMyPreferencesEndpointUrl;
  private String vehicleServiceVehicleByIdEndpointUrl;
  private String vehicleConsentsServiceEndpointUrl;
  private String tripServiceMeEndpointUrl;
  private String tripDetailsServiceMeEndpointUrl;
  private String contentManagementServiceEndpointUrl;
  private String customerApiFindAddressEndpointUrl;
  private String customerApiRetrieveAddressEndpointUrl;
  private String retailerServiceEndpointUrl;
  private String retailerApiServiceTypesEndpointUrl;
  private String retailerApiCitiesEndpointUrl;
  private String retailerApiFavoriteEndpointUrl;
  private String vehiclePositionServiceMeEndpointUrl;
  private String vehicleParkedPositionServiceMeEndpointUrl;

  // Home Charging
  private String homeChargingServiceChargersEndpointUrl;
  private String homeChargingServiceChargersStartEndpointUrl;
  private String homeChargingServiceChargersStopEndpointUrl;
  private String homeChargingServiceChargersStatusEndpointUrl;
  private String homeChargingServiceChargerByIdEndpointUrl;
  private String homeChargingServiceChargerByIdDefaultScheduleEndpointUrl;
  private String homeChargingServiceChargerTariffsEndpointUrl;
  private String homeChargingServiceChargerHistoryEndpointUrl;
  private String homeChargingServiceChargerHistoryLastEndpointUrl;
  private String homeChargingServiceChargerHistoryCsvEndpointUrl;

  // PaaK
  private String paakOwnerPairingEndpointUrl;
  private String paakSuspendKeyEndpointUrl;
  private String paakResumeKeyEndpointUrl;
  private String paakGetPhysicalKeysEndpointUrl;
  private String paakVehicleIdentityMappingEndpointUrl;

  // Public Charging Service
  private String publicChargingServiceUrl;
  private String publicChargingServiceGetFaqsEndpointUrl;
  private String publicChargingServiceGetFiltersEndpointUrl;
  private String publicChargingServiceGetKeywordsEndpointUrl;
  private String publicChargingServiceGetConnectorDetailsEndpointUrl;
  private String publicChargingServiceChargersStartEndpointUrl;
  private String publicChargingServiceChargersStopEndpointUrl;
  private String publicChargingServiceGetChargerDetailsEndpointUrl;
  private String publicChargingServiceGetOccupancyDetailsEndpointUrl;
  private String publicChargingServiceGetUnfinishedOrderEndpointUrl;
  private String publicChargingServiceGetChargingHistoryEndpointUrl;
  private String publicChargingServiceGetChargingSessionHistoryEndpointUrl;
  private String feedbackEndpointUrl;
  private String formEndpointUrl;
  private String deviceVerificationServiceEndpointUrl;
  private String publicChargingServiceGetJlrContractsServiceEndpointUrl;
  private String notificationIntegrationServiceUrl;

  //[CN]Add vehicle service
  private String addVehicleServiceRecognizeUrl;
  private String addVehicleServiceConfirmUrl;

  // Service Booking Service
  private String serviceBookingSubmitUrl;
  private String serviceBookingDetailsUrl;
  private String serviceBookingCancelUrl;
  private String serviceBookingListUrl;
  private String serviceBookingFileUploadUrl;
  private String serviceBookingLastDealerUrl;
  private String serviceBookingServiceAdvisorsUrl;
  private String serviceBookingDealerScheduleUrl;

  // Event Lambda Function Name
  private String eventLambdaFunctionName;

  // Notification Lambda Function Name
  private String notificationLambdaFunctionName;

  // Custom Domain name V2
  private String certificateV2;
  private String domainNameV2;
  private String hostedZoneIdV2;
  private String privateHostedZoneIdV2;
}
