/*
 * Copyright (c) Jaguar Land Rover Ltd 2024. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.api;

import lombok.Getter;

/**
 * Environmental toggle enum, all names should map to the keys from oab-deployment.yaml
 */
@Getter
public enum OabApiGatewayEnvEnum {

  //Map key to /toggle/oab-deployment.yaml
  NOT_SUPPORT_IN_CN_REGION("not_support_in_cn_region"),
  SUPPORT_IN_CN_REGION("support_in_cn_region"),
  EVENT_ENDPOINT("event_endpoint"),
  PAAK_OWNER_PAIRING_ENDPOINT("paak_owner_pairing_endpoint"),
  DOMAIN_NAME_V2("domain_name_v2"),
  CONTENT_SERVICE("content_service"),
  DELETE_VEHICLE_ENDPOINT("delete_vehicle_endpoint"),
  DELETE_USER_ENDPOINT("delete_user_endpoint"),
  VERIFICATIONS_ENDPOINT("verifications_endpoint");

  private final String name;

  OabApiGatewayEnvEnum(String name) {
    this.name = name;
  }
}
