package com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.servicebooking.dealer;

import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.BaseEndpoint;
import java.util.Map;
import software.amazon.awscdk.services.apigateway.Resource;

public class DealerLastOne extends BaseEndpoint {

  public DealerLastOne(Resource resource, String targetUrl, String httpMethod) {
    super(resource, targetUrl, httpMethod);
  }

  @Override
  protected Map<String, String> initializeParamToBePassed() {
    return null;
  }

  @Override
  protected Map<String, Boolean> initializeParamAreRequired() {
    return null;
  }
}
