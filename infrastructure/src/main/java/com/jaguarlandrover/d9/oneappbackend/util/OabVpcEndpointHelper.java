/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.util;

import java.util.List;
import java.util.stream.Collectors;
import software.amazon.awscdk.services.ec2.ISubnet;
import software.amazon.awscdk.services.ec2.Subnet;
import software.constructs.Construct;

public class OabVpcEndpointHelper {


  public static List<ISubnet> stringsToSubnets(Construct scope, List<String> subnetStrings) {
    return subnetStrings.stream().map(subnet -> Subnet.fromSubnetId(scope, subnet, subnet))
        .collect(Collectors.toList());
  }

}
