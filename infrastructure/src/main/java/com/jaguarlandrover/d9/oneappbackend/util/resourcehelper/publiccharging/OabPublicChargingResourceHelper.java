package com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging;

import static com.jaguarlandrover.d9.oneappbackend.api.OabApiGatewayEnvEnum.SUPPORT_IN_CN_REGION;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.CHARGER_START;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.CHARGER_STOP;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.CHARGE_HISTORY;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.CHARGE_OCCUPANCY;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.CHARGE_PATH;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.CHARGE_SESSIONS;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.CHARGE_SESSION_ID;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.CHARGING_PATH;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.COMMAND_PATH;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.CONNECTOR_DETAIL;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.FAQS;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.FILTER;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.FILTERS;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.HTTP_GET_METHOD;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.HTTP_POST_METHOD;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.PUBLIC_PATH;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.STATIONS_PATH;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.STATION_ID_PATH;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.TRENDING_KEYWORDS;

import com.jaguarlandrover.d9.oneappbackend.config.OabApiGatewayAppConfig;
import com.jaguarlandrover.d9.oneappbackend.util.OabDeploymentHelper;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.BaseEndpoint;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.command.StartCharging;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.command.StopCharging;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.order.HistoryChargingDetail;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.order.HistoryChargingFilter;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.order.HistoryChargingOccupancyDetail;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.order.HistoryChargingSessionFilter;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.order.UnfinishedOrder;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.search.ConnectorDetail;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.search.Faqs;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.search.Filters;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.search.StationDetailsCn;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.search.StationDetailsRow;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.search.StationSearch;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.search.TrendingKeywords;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;
import software.amazon.awscdk.services.apigateway.Resource;


@Getter
public class OabPublicChargingResourceHelper {

  private final OabDeploymentHelper deploymentHelper;
  private final OabApiGatewayAppConfig config;
  private final List<BaseEndpoint> endpoints;

  /**
   * Constructor for class OabPublicChargingResourceHelper. Create resource.
   * @param deploymentHelper OabDeploymentHelper class
   * @param version API version
   * @param config Public charging service url
   */
  public OabPublicChargingResourceHelper(OabDeploymentHelper deploymentHelper, Resource version, OabApiGatewayAppConfig config) {
    // set-up env
    this.deploymentHelper = deploymentHelper;
    this.config = config;
    this.endpoints = new ArrayList<>();

    registerEndpoints(version);
  }

  /**
   * Resource only has one instance, duplicate resource will cause error.
   * so we have to create all endpoints here.
   * @param version API version 'api/v1/'
   * */
  private void registerEndpoints(Resource version) {

    // Charging Base Url api/v1/charging/public
    Resource publicCharging = version.addResource(CHARGING_PATH).addResource(PUBLIC_PATH);

    // api/v1/charging/public/faqs
    Resource reFaq = publicCharging.addResource(FAQS);
    // api/v1/charging/public/trending-keywords
    Resource reKeywords = publicCharging.addResource(TRENDING_KEYWORDS);
    // api/v1/charging/public/filters
    Resource reFilter = publicCharging.addResource(FILTERS);
    // api/v1/charging/public/stations
    Resource reStations = publicCharging.addResource(STATIONS_PATH);
    // api/v1/charging/public/stations/{stationId}
    Resource reStationDetailCn = reStations.addResource(STATION_ID_PATH);
    // api/v1/charging/public/stations/{stationId}/connector-detail
    Resource reConnectorDetail = reStationDetailCn.addResource(CONNECTOR_DETAIL);

    // api/v1/charging/public/command
    Resource reCommand = publicCharging.addResource(COMMAND_PATH);
    // api/v1/charging/public/command/sessions
    Resource reSessions = reCommand.addResource(CHARGE_SESSIONS);
    // api/v1/charging/public/command/sessions/{sessionId}
    Resource reSessionId = reSessions.addResource(CHARGE_SESSION_ID);
    // api/v1/charging/public/command/sessions/{sessionId}/start
    Resource reStartCharging = reSessionId.addResource(CHARGER_START);
    // api/v1/charging/public/command/sessions/{sessionId}/stop
    Resource reStopCharging = reSessionId.addResource(CHARGER_STOP);

    // api/v1/charging/public/charge-history
    Resource reHistory = publicCharging.addResource(CHARGE_HISTORY);
    // api/v1/charging/public/charge-history/sessions
    Resource reHistorySessions = reHistory.addResource(CHARGE_SESSIONS);
    // api/v1/charging/public/charge-history/sessions/charge
    Resource reHistoryCharge = reHistorySessions.addResource(CHARGE_PATH);
    // api/v1/charging/public/charge-history/sessions/charge/{sessionId}
    Resource reHistoryChargingDetail = reHistoryCharge.addResource(CHARGE_SESSION_ID);
    // api/v1/charging/public/charge-history/sessions/occupancy
    Resource reOccupancy = reHistorySessions.addResource(CHARGE_OCCUPANCY);
    // api/v1/charging/public/charge-history/sessions/occupancy/:sessionId
    Resource reOccupancyDetail = reOccupancy.addResource(CHARGE_SESSION_ID);

    // api/v1/charging/public/charge-history/:filter
    Resource reHistoryChargingFilter = reHistory.addResource(FILTER);
    // api/v1/charging/public/charge-history/:filter/sessions
    Resource reHistoryChargingSessionFilter = reHistoryChargingFilter.addResource(CHARGE_SESSIONS);

    // Search
    Faqs faqs = new Faqs(reFaq, config.getPublicChargingServiceGetFaqsEndpointUrl(), HTTP_GET_METHOD);
    TrendingKeywords trendingKeywords = new TrendingKeywords(reKeywords,
        config.getPublicChargingServiceGetKeywordsEndpointUrl(), HTTP_GET_METHOD);
    Filters filters = new Filters(reFilter, config.getPublicChargingServiceGetFiltersEndpointUrl(), HTTP_GET_METHOD);
    StationSearch stationSearch = new StationSearch(reStations, config.getPublicChargingServiceUrl(), HTTP_POST_METHOD);
    ConnectorDetail connectorDetail = new ConnectorDetail(reConnectorDetail,
        config.getPublicChargingServiceGetConnectorDetailsEndpointUrl(), HTTP_GET_METHOD);
    BaseEndpoint stationDetails = getStationDetails(reStations, reStationDetailCn);
    // Command
    StartCharging stationStart = new StartCharging(reStartCharging,
        config.getPublicChargingServiceChargersStartEndpointUrl(), HTTP_POST_METHOD);
    StopCharging stationStop = new StopCharging(reStopCharging, config.getPublicChargingServiceChargersStopEndpointUrl(), HTTP_POST_METHOD);
    // History
    HistoryChargingDetail historyChargingDetail = new HistoryChargingDetail(reHistoryChargingDetail,
        config.getPublicChargingServiceGetChargerDetailsEndpointUrl(), HTTP_GET_METHOD);
    HistoryChargingOccupancyDetail historyChargingOccupancyDetail = new HistoryChargingOccupancyDetail(reOccupancyDetail,
        config.getPublicChargingServiceGetOccupancyDetailsEndpointUrl(), HTTP_GET_METHOD);
    UnfinishedOrder unfinishedOrder = new UnfinishedOrder(reHistorySessions,
        config.getPublicChargingServiceGetUnfinishedOrderEndpointUrl(), HTTP_GET_METHOD);
    HistoryChargingFilter historyChargingFilter = new HistoryChargingFilter(reHistoryChargingFilter,
        config.getPublicChargingServiceGetChargingHistoryEndpointUrl(), HTTP_GET_METHOD);
    HistoryChargingSessionFilter historyChargingSessionFilter = new HistoryChargingSessionFilter(reHistoryChargingSessionFilter,
        config.getPublicChargingServiceGetChargingSessionHistoryEndpointUrl(), HTTP_GET_METHOD);

    // Add endpoints
    endpoints.add(stationSearch);
    endpoints.add(stationDetails);
    endpoints.add(faqs);
    endpoints.add(trendingKeywords);
    endpoints.add(filters);
    endpoints.add(connectorDetail);
    endpoints.add(stationStart);
    endpoints.add(stationStop);
    endpoints.add(historyChargingDetail);
    endpoints.add(historyChargingOccupancyDetail);
    endpoints.add(unfinishedOrder);
    endpoints.add(historyChargingFilter);
    endpoints.add(historyChargingSessionFilter);
  }

  /**
   * Station Detailed API.
   * url row: api/v1/charging/public/stations?stationId=xxx
   * url CN: api/v1/charging/public/stations/{stationId}
   */
  private BaseEndpoint getStationDetails(Resource row, Resource cn) {
    String targetUrl = config.getPublicChargingServiceUrl() + "/" + STATION_ID_PATH;
    // Endpoint for ROW public charging service
    StationDetailsRow stationDetailsRow = new StationDetailsRow(row, targetUrl, HTTP_GET_METHOD);
    // Endpoint for CN public charging service
    StationDetailsCn stationDetailsCn = new StationDetailsCn(cn, targetUrl, HTTP_GET_METHOD);

    // stationId is a path param in CN region
    if (deploymentHelper.isAvailable(SUPPORT_IN_CN_REGION)) {
      return stationDetailsCn;
    } else {
      return stationDetailsRow;
    }
  }
}
