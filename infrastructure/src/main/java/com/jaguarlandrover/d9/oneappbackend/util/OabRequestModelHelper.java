/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.util;

import java.util.List;
import java.util.Map;
import software.amazon.awscdk.services.apigateway.JsonSchema;
import software.amazon.awscdk.services.apigateway.JsonSchemaType;
import software.amazon.awscdk.services.apigateway.Model;
import software.amazon.awscdk.services.apigateway.ModelOptions;
import software.amazon.awscdk.services.apigateway.RestApi;
import software.constructs.Construct;


public class OabRequestModelHelper {
  private static JsonSchema getUserBindSchema() {
    return JsonSchema.builder()
        .type(JsonSchemaType.OBJECT)
        .properties(Map.of(
            "eventType", JsonSchema.builder()
                .type(JsonSchemaType.STRING)
                .enumValue(List.of("USER_BIND"))
                .build(),
            "eventDetail", JsonSchema.builder()
                .type(JsonSchemaType.OBJECT)
                .properties(Map.of(
                    "vehicleId", JsonSchema.builder()
                        .type(JsonSchemaType.STRING)
                        .build(),
                    "keyId", JsonSchema.builder()
                        .type(JsonSchemaType.STRING)
                        .build(),
                    "userType", JsonSchema.builder()
                        .type(JsonSchemaType.STRING)
                        .enumValue(List.of("PAAKOWNER", "PAAKFRIEND"))
                        .build(),
                    "exclusiveFrom", JsonSchema.builder()
                        .type(JsonSchemaType.STRING)
                        .build()))
                .required(List.of("vehicleId",  "keyId", "userType", "exclusiveFrom"))
                .build()))
        .required(List.of("eventType", "eventDetail"))
        .build();
  }

  private static JsonSchema getEntitlementUnbindSchema() {
    return JsonSchema.builder()
        .type(JsonSchemaType.OBJECT)
        .properties(Map.of(
            "eventType", JsonSchema.builder()
                .type(JsonSchemaType.STRING)
                .enumValue(List.of("OFFBOARD_UNBIND", "SVT_UNBIND"))
                .build(),
            "eventDetail", JsonSchema.builder()
                .type(JsonSchemaType.OBJECT)
                .properties(Map.of(
                    "vehicleId", JsonSchema.builder()
                        .type(JsonSchemaType.STRING)
                        .build(),
                    "userType", JsonSchema.builder()
                        .type(JsonSchemaType.STRING)
                        .enumValue(List.of("PAAKOWNER", "PAAKFRIEND"))
                        .build(),
                    "exclusiveTo", JsonSchema.builder()
                        .type(JsonSchemaType.STRING)
                        .build()))
                .required(List.of("vehicleId", "userType", "exclusiveTo"))
                .build()))
        .required(List.of("eventType", "eventDetail"))
        .build();
  }

  /**
   * Create model for Event Request.
   * @param scope scope
   * @param api API Gateway
   * @return model for Event Request
   */
  public static Model createEventRequestModel(Construct scope, RestApi api) {
    return api.addModel("EventRequestModel", ModelOptions.builder()
        .contentType("application/json")
        .modelName("EventRequestModel")
        .schema(JsonSchema.builder()
            .oneOf(List.of(getUserBindSchema(), getEntitlementUnbindSchema()))
            .build())
        .build());
  }
}
