/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend;

import static com.jaguarlandrover.d9.oneappbackend.api.OabApiGatewayEnvEnum.DELETE_USER_ENDPOINT;
import static com.jaguarlandrover.d9.oneappbackend.api.OabApiGatewayEnvEnum.DELETE_VEHICLE_ENDPOINT;
import static com.jaguarlandrover.d9.oneappbackend.api.OabApiGatewayEnvEnum.EVENT_ENDPOINT;
import static com.jaguarlandrover.d9.oneappbackend.api.OabApiGatewayEnvEnum.NOT_SUPPORT_IN_CN_REGION;
import static com.jaguarlandrover.d9.oneappbackend.api.OabApiGatewayEnvEnum.PAAK_OWNER_PAIRING_ENDPOINT;
import static com.jaguarlandrover.d9.oneappbackend.api.OabApiGatewayEnvEnum.SUPPORT_IN_CN_REGION;
import static com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants.LAMBDA_CONFIG_TIMEOUT_IN_SECONDS;
import static com.jaguarlandrover.d9.oneappbackend.util.OabDatadogHelper.prepareDataDogTagsByResource;
import static com.jaguarlandrover.d9.oneappbackend.util.OabDatadogHelper.prepareDataDogTagsByStack;
import static com.jaguarlandrover.d9.oneappbackend.util.OabMemachedHelper.MEMCACHED_CLUSTER_NAME;
import static com.jaguarlandrover.d9.oneappbackend.util.OabMemachedHelper.MEMCACHED_PORT;
import static com.jaguarlandrover.d9.oneappbackend.util.OabMemachedHelper.MEMCACHED_SG_DESC;
import static com.jaguarlandrover.d9.oneappbackend.util.OabMemachedHelper.MEMCACHED_SG_NAME;
import static com.jaguarlandrover.d9.oneappbackend.util.OabMemachedHelper.MEMCACHED_SUBNET_GROUP_DESC;
import static com.jaguarlandrover.d9.oneappbackend.util.OabMemachedHelper.MEMCACHED_SUBNET_GROUP_NAME;
import static java.util.Map.entry;

import com.jaguarlandrover.d9.oneappbackend.api.OabApiGatewayEnvEnum;
import com.jaguarlandrover.d9.oneappbackend.api.RestApiConstants;
import com.jaguarlandrover.d9.oneappbackend.config.OabApiGatewayAppConfig;
import com.jaguarlandrover.d9.oneappbackend.config.OabApiGatewaySystemConfig;
import com.jaguarlandrover.d9.oneappbackend.util.CodeHash;
import com.jaguarlandrover.d9.oneappbackend.util.OabApiGatewayHelper;
import com.jaguarlandrover.d9.oneappbackend.util.OabDeploymentHelper;
import com.jaguarlandrover.d9.oneappbackend.util.OabGeofenceServiceResourceHelper;
import com.jaguarlandrover.d9.oneappbackend.util.OabGuardianModeResourceHelper;
import com.jaguarlandrover.d9.oneappbackend.util.OabMemachedHelper;
import com.jaguarlandrover.d9.oneappbackend.util.OabMockIntegrationHelper;
import com.jaguarlandrover.d9.oneappbackend.util.OabRequestModelHelper;
import com.jaguarlandrover.d9.oneappbackend.util.OabVpcEndpointHelper;
import com.jaguarlandrover.d9.oneappbackend.util.OabVpcLinkHelper;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.BaseEndpoint;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.publiccharging.OabPublicChargingResourceHelper;
import com.jaguarlandrover.d9.oneappbackend.util.resourcehelper.servicebooking.OabServiceBookingResourceHelper;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import org.json.JSONObject;
import software.amazon.awscdk.CustomResource;
import software.amazon.awscdk.CustomResourceProps;
import software.amazon.awscdk.Duration;
import software.amazon.awscdk.Stack;
import software.amazon.awscdk.StackProps;
import software.amazon.awscdk.Tags;
import software.amazon.awscdk.services.apigateway.AccessLogField;
import software.amazon.awscdk.services.apigateway.AccessLogFormat;
import software.amazon.awscdk.services.apigateway.EndpointType;
import software.amazon.awscdk.services.apigateway.IVpcLink;
import software.amazon.awscdk.services.apigateway.IdentitySource;
import software.amazon.awscdk.services.apigateway.Integration;
import software.amazon.awscdk.services.apigateway.IntegrationResponse;
import software.amazon.awscdk.services.apigateway.LambdaIntegration;
import software.amazon.awscdk.services.apigateway.LogGroupLogDestination;
import software.amazon.awscdk.services.apigateway.MethodLoggingLevel;
import software.amazon.awscdk.services.apigateway.MethodOptions;
import software.amazon.awscdk.services.apigateway.MethodResponse;
import software.amazon.awscdk.services.apigateway.Model;
import software.amazon.awscdk.services.apigateway.ProxyResource;
import software.amazon.awscdk.services.apigateway.RequestAuthorizer;
import software.amazon.awscdk.services.apigateway.RequestAuthorizerProps;
import software.amazon.awscdk.services.apigateway.Resource;
import software.amazon.awscdk.services.apigateway.RestApi;
import software.amazon.awscdk.services.apigateway.RestApiProps;
import software.amazon.awscdk.services.apigateway.StageOptions;
import software.amazon.awscdk.services.apigateway.VpcLink;
import software.amazon.awscdk.services.ec2.ISubnet;
import software.amazon.awscdk.services.ec2.IVpc;
import software.amazon.awscdk.services.ec2.InterfaceVpcEndpoint;
import software.amazon.awscdk.services.ec2.InterfaceVpcEndpointAwsService;
import software.amazon.awscdk.services.ec2.InterfaceVpcEndpointProps;
import software.amazon.awscdk.services.ec2.Peer;
import software.amazon.awscdk.services.ec2.Port;
import software.amazon.awscdk.services.ec2.SecurityGroup;
import software.amazon.awscdk.services.ec2.SubnetSelection;
import software.amazon.awscdk.services.elasticache.CfnCacheCluster;
import software.amazon.awscdk.services.elasticloadbalancingv2.INetworkLoadBalancerTarget;
import software.amazon.awscdk.services.elasticloadbalancingv2.NetworkTargetGroup;
import software.amazon.awscdk.services.elasticloadbalancingv2.targets.IpTarget;
import software.amazon.awscdk.services.iam.Effect;
import software.amazon.awscdk.services.iam.PolicyStatement;
import software.amazon.awscdk.services.iam.PolicyStatementProps;
import software.amazon.awscdk.services.kms.Key;
import software.amazon.awscdk.services.lambda.Alias;
import software.amazon.awscdk.services.lambda.CfnFunction;
import software.amazon.awscdk.services.lambda.Code;
import software.amazon.awscdk.services.lambda.Function;
import software.amazon.awscdk.services.lambda.FunctionProps;
import software.amazon.awscdk.services.lambda.IFunction;
import software.amazon.awscdk.services.lambda.Runtime;
import software.amazon.awscdk.services.lambda.SingletonFunction;
import software.amazon.awscdk.services.lambda.Version;
import software.amazon.awscdk.services.logs.LogGroup;
import software.amazon.awscdk.services.sqs.IQueue;
import software.amazon.awscdk.services.sqs.Queue;
import software.amazon.awscdk.services.sqs.QueueEncryption;
import software.amazon.awscdk.services.sqs.QueueProps;
import software.constructs.Construct;


public class OabApiGatewayStack extends Stack {

  // KMS
  private static final String ENCRYPT_KEY_NAME_FOR_DLQ = RestApiConstants.APIGW_NAME + "-kms-key-for-lambda-dlq";
  private static final String ENCRYPT_KEY_NAME_FOR_LOGS = RestApiConstants.APIGW_NAME + "-kms-key-for-logs";

  // Lambda Security Group
  private static final String LAMBDA_SG_NAME = RestApiConstants.LAMBDA_AUTHORIZER_FUNC_NAME + "-sg";
  private static final String LAMBDA_CUSTOM_SG_NAME = "lambda-custom-sg";
  private static final String LAMBDA_SG_DESCRIPTION = "Security group for One App lambda function";
  private static final String LAMBDA_CUSTOM_SG_DESCRIPTION = "Security group for the custom lambda function";
  private static final String LAMBDA_DLQ_NAME = RestApiConstants.APIGW_NAME + "-lambda-dead-letter-queue";

  // Stages
  public static final String TRUE = "true";
  public static final String FALSE = "false";
  public static final String DD_LOGS_INJECTION = "DD_LOGS_INJECTION";
  public static final String DD_JMXFETCH_ENABLED = "DD_JMXFETCH_ENABLED";
  public static final String DD_TRACE_ENABLED = "DD_TRACE_ENABLED";

  // Encryption Key for CloudWatch Log Group
  private final Key encryptKeyLogs = Key.Builder.create(this, ENCRYPT_KEY_NAME_FOR_LOGS)
      .alias(ENCRYPT_KEY_NAME_FOR_LOGS)
      .enableKeyRotation(true)
      .build();

  // Encryption Key for DLQ
  private final Key encryptKeyDlq = Key.Builder.create(this, ENCRYPT_KEY_NAME_FOR_DLQ)
      .alias(ENCRYPT_KEY_NAME_FOR_DLQ)
      .enableKeyRotation(true)
      .build();

  // Dead Letter Queue(DLQ)
  final IQueue deadLetterQueue = new Queue(this,
      LAMBDA_DLQ_NAME,
      QueueProps.builder()
          .encryption(QueueEncryption.KMS)
          .encryptionMasterKey(encryptKeyDlq)
          .queueName(LAMBDA_DLQ_NAME)
          .build());

  final Map<String, String> lambdaEnvironmentVariables = new HashMap<>(Map.of(
          DD_LOGS_INJECTION, TRUE,
          DD_JMXFETCH_ENABLED, FALSE,
          DD_TRACE_ENABLED, TRUE
  ));

  /**
   * Constructor for creating API Gateway.
   */
  public OabApiGatewayStack(final Construct scope,
                            final String id,
                            final StackProps props,
                            final OabApiGatewayAppConfig config,
                            final OabApiGatewaySystemConfig systemConfig) {
    super(scope, id, props);

    final OabDeploymentHelper deploymentHelper = new OabDeploymentHelper(config.getDeploymentStage());

    // 1. Create AWS API Gateway
    RestApi restapi = createRestApi(this, config);

    // 2. Create custom domain name and mapping for the api
    OabApiGatewayHelper.createDomainName(this, config, restapi);
    // DDA-104438: create a 2nd customer domain for the API GW for new domain move 
    // Both of these domains will run together for a period of time for cut over
    // Deploying for now only in dev environment:
    if (deploymentHelper.isAvailable(OabApiGatewayEnvEnum.DOMAIN_NAME_V2)) {
      OabApiGatewayHelper.createDomainNameV2(this, config, restapi);
    }

    // 3. VPC
    IVpc lambdaVpc = OabApiGatewayHelper.getVpc(this, config);
    IVpc lambdaPrivateSubnetsVpc = OabApiGatewayHelper.getPrivateSubnetsOfVpc(this, config);

    // 4. Security Groups
    SecurityGroup lambdaSg = OabApiGatewayHelper.createSecurityGroup(this, lambdaVpc,
            LAMBDA_CUSTOM_SG_NAME, LAMBDA_CUSTOM_SG_DESCRIPTION);
    SecurityGroup lambdaPrivateSubnetsSg = OabApiGatewayHelper.createSecurityGroup(this, lambdaVpc,
            LAMBDA_SG_NAME, LAMBDA_SG_DESCRIPTION);

    SecurityGroup memcachedSg = OabApiGatewayHelper.createSecurityGroup(this, lambdaPrivateSubnetsVpc,
        MEMCACHED_SG_NAME, MEMCACHED_SG_DESC);

    // 4.1 Allow ingress from lambda sg to memcached sg
    memcachedSg.addIngressRule(
        Peer.securityGroupId(lambdaPrivateSubnetsSg.getSecurityGroupId()),
        Port.tcp(MEMCACHED_PORT),
        "Allow inbound traffic from lambdas");

    // 5. VPC Links

    // 5.1 Create VPC endpoint in MAB
    List<ISubnet> subnets =
        OabVpcEndpointHelper.stringsToSubnets(this, config.getSubnetIds());
    SubnetSelection subnetSelection = SubnetSelection.builder()
        .subnets(subnets)
        .build();

    SingletonFunction eniCustomResourceFn = OabVpcLinkHelper.createEniCustomResourceLambda(this, lambdaVpc, lambdaSg);
    eniCustomResourceFn.addToRolePolicy(new PolicyStatement(PolicyStatementProps.builder()
        .actions(List.of("ec2:DescribeNetworkInterfaces"))
        .effect(Effect.ALLOW)
        .resources(List.of("*"))
        .build()));

    InterfaceVpcEndpoint apigwVpcEndpoint =
        new InterfaceVpcEndpoint(this, "apigw-vpc-endpoint", InterfaceVpcEndpointProps.builder()
            .service(new InterfaceVpcEndpointAwsService(config.getApigwVpcEndpoint()))
            .vpc(lambdaVpc)
            .subnets(subnetSelection)
            .build());

    CustomResource eniCustomResource =
        new CustomResource(this, "ENIPrivateIPResource", CustomResourceProps.builder()
            .serviceToken(eniCustomResourceFn.getFunctionArn())
            .properties(Map.of("vpce_enis", apigwVpcEndpoint.getVpcEndpointNetworkInterfaceIds()))
            .build());

    // Target Group for VPC endpoint
    List<INetworkLoadBalancerTarget> targets = List.of(
        new IpTarget(eniCustomResource.getAttString("IP0")),
        new IpTarget(eniCustomResource.getAttString("IP1")));
    NetworkTargetGroup vcdpTargetGroup = OabVpcLinkHelper.createTargetGroup(this,
        "vcdp",
        lambdaVpc,
        targets);

    // 5.2 Create VPC Link connecting to MAB VPC endpoint (Use this for calling VCDP API Gateways Execute API Endpoints)
    final VpcLink vcdpEndpointVpcLink = OabVpcLinkHelper.createVpcLink(this,
        "vcdp",
        lambdaVpc,
        subnetSelection,
        vcdpTargetGroup);

    // 5.3 Create VPC Link connecting to MAB EKS cluster
    final VpcLink mabNginxVpcLink = OabVpcLinkHelper.createVpcLink(this,
        OabApiGatewayHelper.getNlb(this,
            config.getNginxNlbArn()),
        "mab-nginx-vpc-link",
        "mab-eks-nginx-vpc-link",
        "VPC Link to MAB eks cluster services");

    // 5.4 Create VPC Link connecting to Eco-Int
    List<INetworkLoadBalancerTarget> ecoIntTargets = new ArrayList<>();
    systemConfig.getEcoIntVpceIps().forEach(s -> ecoIntTargets.add(new IpTarget(s,null, "all")));
    NetworkTargetGroup ecoIntTargetGroup = OabVpcLinkHelper.createTargetGroup(this,
        "eco-int",
        lambdaVpc,
        ecoIntTargets);

    final VpcLink ecoIntEndpointVpcLink = OabVpcLinkHelper.createVpcLink(this,
        "eco-int",
        lambdaVpc,
        subnetSelection,
        ecoIntTargetGroup);

    // 6. Create Memcached cluster
    CfnCacheCluster cacheCluster = OabMemachedHelper.createCacheCluster(this,
        MEMCACHED_CLUSTER_NAME,
        MEMCACHED_SUBNET_GROUP_NAME,
        MEMCACHED_SUBNET_GROUP_DESC,
        List.of(memcachedSg.getSecurityGroupId()),
        config);

    // 7. ForgeRock Lambda Authorizer
    LogGroup lambdaAuthorizerLogGroup = OabApiGatewayHelper.createLogGroup(this,
        RestApiConstants.LAMBDA_AUTHORIZER_LOG_GROUP_NAME,
        config.getRegion(),
        encryptKeyLogs);
    IFunction lambdaAuthorizer = getAuthorizerFunction(config,
        lambdaPrivateSubnetsVpc,
        lambdaPrivateSubnetsSg,
        deadLetterQueue,
        cacheCluster,
        lambdaAuthorizerLogGroup,
        deploymentHelper);
    final RequestAuthorizer authorizer = createFrLambdaAuthorizer(lambdaAuthorizer);

    // 8. Set the Datadog unified tags
    // 8.1 For resources in this stack
    String ddEnv = config.getDdEnv();
    String ddVersion = systemConfig.getDdVersion();
    prepareDataDogTagsByStack(this,
        ddEnv,
        config.getDdService(),
        ddVersion,
        config.getDdServiceLambdaAuthorizer(),
        config.getDdManagedBy(),
        config.getDdPiiDataHandler(),
        config.getDdProductOwner(),
        config.getDdRepoUrl(),
        config.getDdSquad());
    // 8.2 For Security Group of the Lambda Authorizer Function
    prepareDataDogTagsByResource(lambdaPrivateSubnetsSg,
        ddEnv,
        config.getDdServiceLambdaAuthorizer(),
        ddVersion,
        config.getDdManagedBy(),
        config.getDdPiiDataHandler(),
        config.getDdProductOwner(),
        config.getDdRepoUrl(),
        config.getDdSquad());
    // 8.3 For Log Group of the Lambda Authorizer Function
    prepareDataDogTagsByResource(lambdaAuthorizerLogGroup,
        ddEnv,
        config.getDdServiceLambdaAuthorizer(),
        ddVersion,
        config.getDdManagedBy(),
        config.getDdPiiDataHandler(),
        config.getDdProductOwner(),
        config.getDdRepoUrl(),
        config.getDdSquad());

    // 8.4 WAFPolicy Tag only for CN: it will bind WAF for gateway API when set as "Core"
    if (deploymentHelper.isAvailable(SUPPORT_IN_CN_REGION)) {
      Tags.of(restapi).add(RestApiConstants.WAF_POLICY_TAG, "Core");
    }

    // 9. Mock Integration for all other undefined endpoints should return 404 status code
    Resource proxyResource = restapi.getRoot().addResource(RestApiConstants.API_PROXY_PATH);
    OabMockIntegrationHelper.addIntegrationForUndefinedPaths(proxyResource);

    // 10. Define API endpoint
    Resource api = restapi.getRoot().addResource(RestApiConstants.API);
    Resource version = api.addResource(RestApiConstants.VERSION);

    Map<String, String> requestIdHeaderParameters = Map.of("integration.request.header.x-amzn-RequestId", "context.requestId");

    // 11. Users endpoints
    Map<String, String> requestParameters = new HashMap<>();
    requestParameters.put("integration.request.path.id", "context.authorizer.principalId");
    requestParameters.putAll(requestIdHeaderParameters);

    Resource users = version.addResource(RestApiConstants.USERS_PATH);

    VpcLink userServiceVpcLink = deploymentHelper.isAvailable(OabApiGatewayEnvEnum.SUPPORT_IN_CN_REGION)
        ? vcdpEndpointVpcLink : mabNginxVpcLink;

    // 10.1 Users register endpoint
    addResourceMethod(RestApiConstants.HTTP_POST_METHOD, users, config.getUserServiceUsersRegisterEndpointUrl(), authorizer,
            userServiceVpcLink, requestParameters, null);
    // 10.2 Users ME endpoint
    Resource me = users.addResource(RestApiConstants.ME_PATH);
    addResourceMethod(RestApiConstants.HTTP_GET_METHOD, me, config.getUserServiceUsersEndpointUrl(), authorizer, userServiceVpcLink,
        requestParameters, null);
    // 10.3 Users update endpoint
    addResourceMethod(RestApiConstants.HTTP_PUT_METHOD, me, config.getUserServiceUsersUpdateEndpointUrl(), authorizer, userServiceVpcLink,
        requestParameters, null);
    // 10.4 Users delete endpoint
    addResourceMethod(
        RestApiConstants.HTTP_DELETE_METHOD,
        me,
        config.getUserServiceUsersDeleteEndpointUrl(),
        authorizer,
        userServiceVpcLink,
        requestParameters,
        null);

    // 10.5 Users avatar signed url endpoint
    if (deploymentHelper.isAvailable(SUPPORT_IN_CN_REGION)) {
      Resource avatar = me.addResource(RestApiConstants.AVATAR_PATH);
      addResourceMethod(
          RestApiConstants.HTTP_GET_METHOD,
          avatar,
          config.getUserServiceUsersAvatarEndpointUrl(),
          authorizer,
          userServiceVpcLink,
          requestParameters,
          null);
    }


    // 11.1 User consents retrieve endpoint
    Resource consents = users.addResource(RestApiConstants.CONSENTS_PATH);
    addResourceMethod(RestApiConstants.HTTP_GET_METHOD, consents, config.getUserServiceUsersRetrieveConsentsEndpointUrl(), authorizer,
            userServiceVpcLink, requestParameters, null);
    // 11.2 User consents update endpoint
    addResourceMethod(RestApiConstants.HTTP_PATCH_METHOD, consents, config.getUserServiceUsersUpdateConsentsEndpointUrl(), authorizer,
            userServiceVpcLink, requestParameters, null);

    // 12. Features ME endpoint
    Resource features = me.addResource(RestApiConstants.FEATURES_PATH);

    // Connect to Feature Service
    addResourceMethod(RestApiConstants.HTTP_GET_METHOD, features, config.getFeatureServiceMeEndpointUrl(), authorizer, mabNginxVpcLink,
        requestParameters, null);

    // 13. Vehicles ME endpoint
    Resource userVehicles = me.addResource(RestApiConstants.VEHICLES_PATH);

    Map<String, String> passedVehicleParameters = new HashMap<>(requestIdHeaderParameters);
    addResourceMethod(RestApiConstants.HTTP_GET_METHOD, userVehicles, config.getVehicleServiceMyVehiclesEndpointUrl(), authorizer,
        mabNginxVpcLink, passedVehicleParameters, null);

    // 13.1 Vehicles update endpoint
    Resource userVehicleById = userVehicles.addResource(RestApiConstants.VEHICLE_ID_PATH);

    passedVehicleParameters.put("integration.request.path.vehicleId", "method.request.path.vehicleId");

    Map<String, Boolean> requiredVehicleParameters = new HashMap<>();
    requiredVehicleParameters.put("method.request.path.vehicleId", true);

    addResourceMethod(
        RestApiConstants.HTTP_PATCH_METHOD,
        userVehicleById,
        config.getVehicleServiceVehicleByIdEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        passedVehicleParameters,
        requiredVehicleParameters);

    // 13.2 Vehicles delete endpoint
    if (deploymentHelper.isAvailable(DELETE_VEHICLE_ENDPOINT)) {
      addResourceMethod(
          RestApiConstants.HTTP_DELETE_METHOD,
          userVehicleById,
          config.getVehicleServiceVehicleByIdEndpointUrl(),
          authorizer,
          mabNginxVpcLink,
          passedVehicleParameters,
          requiredVehicleParameters);
    }

    // 14. Home Charging endpoints
    Resource charging = me.addResource(RestApiConstants.CHARGING_PATH);
    Resource homeCharging = charging.addResource(RestApiConstants.HOME_PATH);
    Resource homeChargers = homeCharging.addResource(RestApiConstants.CHARGERS_PATH);

    // 14.1 Link charger
    addResourceMethod(
        RestApiConstants.HTTP_POST_METHOD,
        homeChargers,
        config.getHomeChargingServiceChargersEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        null,
        null
    );

    // 14.2 List users chargers
    addResourceMethod(
        RestApiConstants.HTTP_GET_METHOD,
        homeChargers,
        config.getHomeChargingServiceChargersEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        null,
        null
    );

    Resource homeChargerById = homeChargers.addResource(RestApiConstants.CHARGER_ID_PATH);
    Map<String, String> requestChargerIdParameter = new HashMap<>();
    requestChargerIdParameter.put("integration.request.path.chargerId", "method.request.path.chargerId");

    Map<String, Boolean> requestChargerIdParameterOptions = new HashMap<>();
    requestChargerIdParameterOptions.put("method.request.path.chargerId", true);

    // 14.3 Start/boost charging
    Resource homeChargerStart = homeChargerById.addResource(RestApiConstants.CHARGER_START);

    addResourceMethod(
        RestApiConstants.HTTP_POST_METHOD,
        homeChargerStart,
        config.getHomeChargingServiceChargersStartEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        requestChargerIdParameter,
        requestChargerIdParameterOptions
    );

    // 14.4 Stop charging
    Resource homeChargerStop = homeChargerById.addResource(RestApiConstants.CHARGER_STOP);

    addResourceMethod(
        RestApiConstants.HTTP_POST_METHOD,
        homeChargerStop,
        config.getHomeChargingServiceChargersStopEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        requestChargerIdParameter,
        requestChargerIdParameterOptions
    );

    // 14.5 Get charger details by ID
    addResourceMethod(
        RestApiConstants.HTTP_GET_METHOD,
        homeChargerById,
        config.getHomeChargingServiceChargerByIdEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        requestChargerIdParameter,
        requestChargerIdParameterOptions
    );

    // 14.6 Update charger details by ID
    addResourceMethod(
        RestApiConstants.HTTP_PATCH_METHOD,
        homeChargerById,
        config.getHomeChargingServiceChargerByIdEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        requestChargerIdParameter,
        requestChargerIdParameterOptions
    );

    // 14.7 Charge Point Status
    Resource homeChargerStatus = homeChargerById.addResource(RestApiConstants.CHARGER_STATUS);

    addResourceMethod(
        RestApiConstants.HTTP_GET_METHOD,
        homeChargerStatus,
        config.getHomeChargingServiceChargersStatusEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        requestChargerIdParameter,
        requestChargerIdParameterOptions
    );

    // 14.8 Schedules
    Resource schedules = homeChargerById.addResource(RestApiConstants.CHARGER_SCHEDULES);
    Resource defaultSchedules = schedules.addResource(RestApiConstants.CHARGER_SCHEDULES_DEFAULT);

    addResourceMethod(
        RestApiConstants.HTTP_GET_METHOD,
        defaultSchedules,
        config.getHomeChargingServiceChargerByIdDefaultScheduleEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        requestChargerIdParameter,
        requestChargerIdParameterOptions
    );

    // 14.9 Update default schedule
    addResourceMethod(
        RestApiConstants.HTTP_PATCH_METHOD,
        defaultSchedules,
        config.getHomeChargingServiceChargerByIdDefaultScheduleEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        requestChargerIdParameter,
        requestChargerIdParameterOptions
    );

    // 14.10 Delete default schedule
    addResourceMethod(
        RestApiConstants.HTTP_DELETE_METHOD,
        defaultSchedules,
        config.getHomeChargingServiceChargerByIdDefaultScheduleEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        requestChargerIdParameter,
        requestChargerIdParameterOptions
    );

    // 14.11 Tariffs
    Resource tariffs = homeChargerById.addResource(RestApiConstants.CHARGER_TARIFFS);

    addResourceMethod(
        RestApiConstants.HTTP_GET_METHOD,
        tariffs,
        config.getHomeChargingServiceChargerTariffsEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        requestChargerIdParameter,
        requestChargerIdParameterOptions
    );

    addResourceMethod(
        RestApiConstants.HTTP_POST_METHOD,
        tariffs,
        config.getHomeChargingServiceChargerTariffsEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        requestChargerIdParameter,
        requestChargerIdParameterOptions
    );

    addResourceMethod(
        RestApiConstants.HTTP_PATCH_METHOD,
        tariffs,
        config.getHomeChargingServiceChargerTariffsEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        requestChargerIdParameter,
        requestChargerIdParameterOptions
    );

    addResourceMethod(
        RestApiConstants.HTTP_DELETE_METHOD,
        tariffs,
        config.getHomeChargingServiceChargerTariffsEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        requestChargerIdParameter,
        requestChargerIdParameterOptions
    );

    //Charger History
    Resource history = homeChargerById.addResource(RestApiConstants.CHARGER_HISTORY);

    addResourceMethod(
        RestApiConstants.HTTP_GET_METHOD,
        history,
        config.getHomeChargingServiceChargerHistoryEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        requestChargerIdParameter,
        requestChargerIdParameterOptions
    );

    //Charger History last session
    Resource historyLastSession = history.addResource(RestApiConstants.CHARGER_HISTORY_LAST);

    addResourceMethod(
        RestApiConstants.HTTP_GET_METHOD,
        historyLastSession,
        config.getHomeChargingServiceChargerHistoryLastEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        requestChargerIdParameter,
        requestChargerIdParameterOptions
    );

    //Charger History CSV
    Resource historyCsv = history.addResource(RestApiConstants.CHARGER_HISTORY_CSV);

    addResourceMethod(
        RestApiConstants.HTTP_GET_METHOD,
        historyCsv,
        config.getHomeChargingServiceChargerHistoryCsvEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        requestChargerIdParameter,
        requestChargerIdParameterOptions
    );

    // 15. Vehicles endpoint (for commands etc)
    Resource vehicles = version.addResource(RestApiConstants.VEHICLES_PATH);
    Resource vehiclesId = vehicles.addResource(RestApiConstants.VEHICLE_ID_PATH);

    Map<String, String> lambdaEnvironment = new HashMap<>(Map.of(
        "account", config.getAccount(),
        "region", config.getRegion(),
        "commandRouterHost", config.getCommandRouterUrl()
    ));
    lambdaEnvironment.putAll(lambdaEnvironmentVariables);
    Function proxyRouterHandlerFunction =
        createRouterHandlerFunction(id, lambdaVpc, lambdaSg, subnetSelection, lambdaEnvironment);

    LambdaIntegration proxyLambdaIntegration = createSnapStartIntegration(this,
        proxyRouterHandlerFunction,
        "../software/commandsproxyhandler",
        "CommandProxy",
        deploymentHelper);

    // 16.1 Vehicle ID Commands endpoint (Integration with Command Router API Gateway)
    Resource commandsByVehicle = vehiclesId.addResource(RestApiConstants.COMMANDS_PATH);

    ProxyResource commandsProxy = commandsByVehicle.addProxy();
    commandsProxy.addMethod(RestApiConstants.HTTP_POST_METHOD,
        proxyLambdaIntegration,
        getMethodOptions(authorizer, null, null));

    // 16.2 Vehicle ID Consents endpoint (Integration with Command Router API Gateway)
    Resource vehicleConsents = vehiclesId.addResource(RestApiConstants.CONSENTS_PATH);

    // 16.2.1 GET
    addResourceMethod(
        RestApiConstants.HTTP_GET_METHOD,
        vehicleConsents,
        config.getVehicleConsentsServiceEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        passedVehicleParameters,
        requiredVehicleParameters);

    // 16.2.1 PATCH
    addResourceMethod(RestApiConstants.HTTP_PATCH_METHOD,
        vehicleConsents,
        config.getVehicleConsentsServiceEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        passedVehicleParameters,
        requiredVehicleParameters);

    // 17.1 Vehicle Position endpoint by Vehicle ID
    Resource vehiclePosition = userVehicleById.addResource(RestApiConstants.VEHICLE_POSITION_PATH);
    Resource vehiclePositionLatest = vehiclePosition.addResource(RestApiConstants.VEHICLE_POSITION_LATEST_PATH);
    // Connect to Vehicle Position Service
    addResourceMethod(
            RestApiConstants.HTTP_GET_METHOD,
            vehiclePositionLatest,
            config.getVehiclePositionServiceMeEndpointUrl(),
            authorizer,
            ecoIntEndpointVpcLink,
            passedVehicleParameters,
            requiredVehicleParameters);

    // 17.2 Vehicle Parked Position endpoint by Vehicle ID.
    Resource vehiclePositionParked = vehiclePosition.addResource(RestApiConstants.VEHICLE_POSITION_PARKED_PATH);
    // Connect to Vehicle Position Service
    addResourceMethod(
        RestApiConstants.HTTP_GET_METHOD,
        vehiclePositionParked,
        config.getVehicleParkedPositionServiceMeEndpointUrl(),
        authorizer,
        ecoIntEndpointVpcLink,
        passedVehicleParameters,
        requiredVehicleParameters);

    // 18.1 Trips endpoint by Vehicle ID
    Resource trips = userVehicleById.addResource(RestApiConstants.TRIPS_PATH);
    addResourceMethod(RestApiConstants.HTTP_GET_METHOD, trips, config.getTripServiceMeEndpointUrl(), authorizer,
            ecoIntEndpointVpcLink, passedVehicleParameters, requiredVehicleParameters);

    // 18.2 Trip Details endpoint by Vehicle ID
    Resource tripById = trips.addResource(RestApiConstants.TRIP_ID_PATH);

    // Added tripId to the integration and method request parameters
    passedVehicleParameters.put("integration.request.path.tripId", "method.request.path.tripId");
    requiredVehicleParameters.put("method.request.path.tripId", true);

    // Connect to Trip Details Service
    addResourceMethod(RestApiConstants.HTTP_GET_METHOD, tripById, config.getTripDetailsServiceMeEndpointUrl(), authorizer,
            ecoIntEndpointVpcLink, passedVehicleParameters, requiredVehicleParameters);

    // 19. Retailer search endpoint
    Resource retailers = version.addResource(RestApiConstants.RETAILERS_PATH);
    if (deploymentHelper.isAvailable(OabApiGatewayEnvEnum.SUPPORT_IN_CN_REGION)) {
      final Resource retailersServiceTypes = retailers.addResource(
          RestApiConstants.RETAILERS_SERVICE_TYPES_PATH);
      final Resource retailersCities = retailers.addResource(RestApiConstants.RETAILERS_CITIES_PATH);
      final Resource retailersFavorite = retailers.addResource(RestApiConstants.RETAILERS_CODE_PATH)
          .addResource(RestApiConstants.RETAILERS_FAVORITE_PATH);
      Map<String, String> passedRetailerParameters = new HashMap<>(requestIdHeaderParameters);
      passedRetailerParameters.put("integration.request.path.tradingCode", "method.request.path.tradingCode");
      Map<String, Boolean> requiredRetailerParameters = new HashMap<>();
      requiredRetailerParameters.put("method.request.path.tradingCode", true);
      addResourceMethod(RestApiConstants.HTTP_POST_METHOD, retailers, config.getRetailerServiceEndpointUrl(), authorizer,
          vcdpEndpointVpcLink, requestParameters, null);
      addResourceMethod(RestApiConstants.HTTP_GET_METHOD, retailersServiceTypes, config.getRetailerApiServiceTypesEndpointUrl(), authorizer,
          vcdpEndpointVpcLink, requestParameters, null);
      addResourceMethod(RestApiConstants.HTTP_GET_METHOD, retailersCities, config.getRetailerApiCitiesEndpointUrl(), authorizer,
          vcdpEndpointVpcLink, requestParameters, null);
      addResourceMethod(RestApiConstants.HTTP_PUT_METHOD, retailersFavorite, config.getRetailerApiFavoriteEndpointUrl(), authorizer,
          vcdpEndpointVpcLink, passedRetailerParameters, requiredRetailerParameters);
      addResourceMethod(RestApiConstants.HTTP_DELETE_METHOD, retailersFavorite, config.getRetailerApiFavoriteEndpointUrl(), authorizer,
          vcdpEndpointVpcLink, passedRetailerParameters, requiredRetailerParameters);
    } else {
      addIntegrationMethodForRetailer(retailers, authorizer, config);
    }

    // 20. Event endpoint
    if (deploymentHelper.isAvailable(EVENT_ENDPOINT)) {
      Resource event = version.addResource(RestApiConstants.EVENT_PATH);
      Model eventRequestModel = OabRequestModelHelper.createEventRequestModel(scope, restapi);
      addLambdaIntegrationMethod(this, RestApiConstants.HTTP_POST_METHOD, event, config.getEventLambdaFunctionName(),
                                 authorizer, eventRequestModel);
    }

    // 21.1. JLR Charging Contract endpoint
    //  GET
    Resource userContracts = me.addResource(RestApiConstants.CONTRACTS_PATH);
    addResourceMethod(
        RestApiConstants.HTTP_GET_METHOD,
        userContracts,
        config.getPublicChargingServiceGetJlrContractsServiceEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        null,
        null);

    //21.2 Public charger search endpoint
    final OabPublicChargingResourceHelper pcResource = new OabPublicChargingResourceHelper(
        deploymentHelper, version, config);
    for (BaseEndpoint baseEndpoint: pcResource.getEndpoints()) {
      addResourceMethodByBaseEndPoint(authorizer, mabNginxVpcLink, baseEndpoint);
    }

    // 22. PaaK Owner pairing endpoint
    if (deploymentHelper.isAvailable(PAAK_OWNER_PAIRING_ENDPOINT)) {
      Resource paak = me.addResource(RestApiConstants.PAAK_PATH);
      Resource paakVehicles = paak.addResource(RestApiConstants.VEHICLES_PATH);
      Resource paakVehicleId = paakVehicles.addResource(RestApiConstants.VEHICLE_ID_PATH);
      Resource paakPairing = paakVehicleId.addResource(RestApiConstants.PAIRING_PATH);

      Map<String, Boolean> paakRequestParameters = new HashMap<>();
      paakRequestParameters.put("method.request.path.vehicleId", true);

      Map<String, String> paakIntegrationRequestParameters = new HashMap<>();
      paakIntegrationRequestParameters.put("integration.request.path.vehicleId", "method.request.path.vehicleId");

      addResourceMethod(RestApiConstants.HTTP_POST_METHOD, paakPairing, config.getPaakOwnerPairingEndpointUrl(), authorizer,
              vcdpEndpointVpcLink, paakIntegrationRequestParameters, paakRequestParameters);

      Resource paakKeys = paakVehicleId.addResource(RestApiConstants.KEYS_PATH);
      Resource paakGetPhysicalKeys = paakKeys.addResource(RestApiConstants.PHYSICAL_PATH);
      addResourceMethod(RestApiConstants.HTTP_GET_METHOD, paakGetPhysicalKeys, config.getPaakGetPhysicalKeysEndpointUrl(),
              authorizer, vcdpEndpointVpcLink, paakIntegrationRequestParameters, paakRequestParameters);

      Map<String, Boolean> paakKeyManagementRequestParameters = new HashMap<>();
      paakKeyManagementRequestParameters.put("method.request.path.vehicleId", true);
      paakKeyManagementRequestParameters.put("method.request.path.slotId", true);

      Map<String, String> paakKeyManagementIntegrationRequestParameters = new HashMap<>();
      paakKeyManagementIntegrationRequestParameters.put("integration.request.path.vehicleId", "method.request.path.vehicleId");
      paakKeyManagementIntegrationRequestParameters.put("integration.request.path.slotId", "method.request.path.slotId");

      Resource paakSlotId = paakKeys.addResource(RestApiConstants.SLOT_ID_PATH);
      Resource paakSuspendKey = paakSlotId.addResource(RestApiConstants.SUSPEND_PATH);
      addResourceMethod(RestApiConstants.HTTP_POST_METHOD, paakSuspendKey, config.getPaakSuspendKeyEndpointUrl(), authorizer,
              vcdpEndpointVpcLink, paakKeyManagementIntegrationRequestParameters, paakKeyManagementRequestParameters);

      Resource paakResumeKey = paakSlotId.addResource(RestApiConstants.RESUME_PATH);
      addResourceMethod(RestApiConstants.HTTP_POST_METHOD, paakResumeKey, config.getPaakResumeKeyEndpointUrl(), authorizer,
              vcdpEndpointVpcLink, paakKeyManagementIntegrationRequestParameters, paakKeyManagementRequestParameters);

      Resource paakIdentityMapping = paakVehicles.addResource(RestApiConstants.IDENTITY_MAPPING);
      addResourceMethod(RestApiConstants.HTTP_POST_METHOD, paakIdentityMapping,
          config.getPaakVehicleIdentityMappingEndpointUrl(), authorizer,
          vcdpEndpointVpcLink, null, null);
    }

    // 23. Preferences ME endpoint
    Resource userPreferences = me.addResource(RestApiConstants.PREFERENCES_PATH);
    // 23.1 Preferences get endpoint
    addResourceMethod(
        RestApiConstants.HTTP_GET_METHOD,
        userPreferences,
        config.getPreferenceServiceMyPreferencesEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        requestIdHeaderParameters,
        null);

    // 23.2 Preferences patch endpoint
    addResourceMethod(
        RestApiConstants.HTTP_PATCH_METHOD,
        userPreferences,
        config.getPreferenceServiceMyPreferencesEndpointUrl(),
        authorizer,
        mabNginxVpcLink,
        null,
        null);

    // 24.1 RoW Notification endpoint
    if (deploymentHelper.isAvailable(NOT_SUPPORT_IN_CN_REGION)) {
      Resource notification = me.addResource(RestApiConstants.DEVICES);
      Resource notificationById = notification.addResource(RestApiConstants.DEVICE_ID_PATH);

      Integration notificationLambdaIntegration = OabApiGatewayHelper.createLambdaIntegration(
              this,
              config.getNotificationLambdaFunctionName());
      Map<String, Boolean> notificationRequestParameters = new HashMap<>();
      notificationRequestParameters.put("method.request.path.device_id", true);

      notification.addMethod(
          RestApiConstants.HTTP_POST_METHOD,
          notificationLambdaIntegration,
          getMethodOptions(authorizer, null, null));

      notificationById.addMethod(
              RestApiConstants.HTTP_PUT_METHOD,
              notificationLambdaIntegration,
              getMethodOptions(authorizer, notificationRequestParameters, null));

      notificationById.addMethod(
              RestApiConstants.HTTP_DELETE_METHOD,
              notificationLambdaIntegration,
              getMethodOptions(authorizer, notificationRequestParameters, null));
    }
    // 24.2 CN Notification endpoint
    if (deploymentHelper.isAvailable(SUPPORT_IN_CN_REGION)) {
      Resource notification = me.addResource(RestApiConstants.DEVICES);
      addResourceMethod(RestApiConstants.HTTP_POST_METHOD, notification,
          config.getNotificationIntegrationServiceUrl(),
          authorizer, vcdpEndpointVpcLink, requestParameters, null);
      addResourceMethod(RestApiConstants.HTTP_DELETE_METHOD, notification,
          config.getNotificationIntegrationServiceUrl(),
          authorizer, vcdpEndpointVpcLink, requestParameters, null);
    }

    // 25. Schedule Endpoint
    Resource scheduling = version.addResource(RestApiConstants.SCHEDULES_PATH);

    final Resource schedulesVehicleId = scheduling.addResource(RestApiConstants.VEHICLE_ID_PATH);

    Map<String, Boolean> scheduleRequestParameters = new HashMap<>();
    scheduleRequestParameters.put("method.request.path.vehicleId", true);
    scheduleRequestParameters.put("method.request.header.Authorization", true);
    scheduleRequestParameters.put("method.request.header.Correlation-Id", false);

    Map<String, String> scheduleIntegrationRequestParameters = new HashMap<>();
    scheduleIntegrationRequestParameters.put("integration.request.path.vehicleId", "method.request.path.vehicleId");
    scheduleIntegrationRequestParameters.put("integration.request.header.Authorization","method.request.header.Authorization");
    scheduleIntegrationRequestParameters.put("integration.request.header.Correlation-Id","context.requestId");

    // Disable http_proxy or else we get 400 error
    Optional<Boolean> enableVpcProxyIntegration = Optional.of(false);

    // GET
    addResourceMethod(
        RestApiConstants.HTTP_GET_METHOD,
        schedulesVehicleId,
        config.getScheduleServiceUrl(),
        authorizer,
        vcdpEndpointVpcLink,
        scheduleIntegrationRequestParameters,
        scheduleRequestParameters,
        enableVpcProxyIntegration,
        null,
        null
    );

    // 25.1 schedules departure endpoint
    String departuresPath = "/departures/routines";
    Resource departure = schedulesVehicleId.addResource(RestApiConstants.DEPARTURES_PATH);
    Resource routines = departure.addResource(RestApiConstants.ROUTINES_PATH);

    // POST
    addResourceMethod(
        RestApiConstants.HTTP_POST_METHOD,
        routines,
        config.getScheduleServiceUrl() + departuresPath,
        authorizer,
        vcdpEndpointVpcLink,
        scheduleIntegrationRequestParameters,
        scheduleRequestParameters,
        enableVpcProxyIntegration,
        null,
        null
    );

    // DELETE
    addResourceMethod(
        RestApiConstants.HTTP_DELETE_METHOD,
        routines,
        config.getScheduleServiceUrl() + departuresPath,
        authorizer,
        vcdpEndpointVpcLink,
        scheduleIntegrationRequestParameters,
        scheduleRequestParameters,
        enableVpcProxyIntegration,
        null,
        null
    );

    // PUT
    addResourceMethod(
        RestApiConstants.HTTP_PUT_METHOD,
        routines,
        config.getScheduleServiceUrl() + departuresPath,
        authorizer,
        vcdpEndpointVpcLink,
        scheduleIntegrationRequestParameters,
        scheduleRequestParameters,
        enableVpcProxyIntegration,
        null,
        null
    );

    // 25.2 schedules charging endpoints
    Resource chargingSchedules = schedulesVehicleId.addResource(RestApiConstants.CHARGING_PATH);

    // DELETE
    addResourceMethod(
        RestApiConstants.HTTP_DELETE_METHOD,
        chargingSchedules,
        config.getScheduleServiceUrl() + "/charging",
        authorizer,
        vcdpEndpointVpcLink,
        scheduleIntegrationRequestParameters,
        scheduleRequestParameters,
        enableVpcProxyIntegration,
        null,
        null
    );

    Resource offPeak = chargingSchedules.addResource(RestApiConstants.OFF_PEAK_PATH);

    // POST
    addResourceMethod(
        RestApiConstants.HTTP_POST_METHOD,
        offPeak,
        config.getScheduleServiceUrl() + "/charging/off-peak",
        authorizer,
        vcdpEndpointVpcLink,
        scheduleIntegrationRequestParameters,
        scheduleRequestParameters,
        enableVpcProxyIntegration,
        null,
        null
    );

    Resource smart = chargingSchedules.addResource(RestApiConstants.SMART_PATH);

    // POST
    addResourceMethod(
        RestApiConstants.HTTP_POST_METHOD,
        smart,
        config.getScheduleServiceUrl() + "/charging/smart",
        authorizer,
        vcdpEndpointVpcLink,
        scheduleIntegrationRequestParameters,
        scheduleRequestParameters,
        enableVpcProxyIntegration,
        null,
        null
    );

    if (deploymentHelper.isAvailable(OabApiGatewayEnvEnum.SUPPORT_IN_CN_REGION)) {
      Resource feedback = version.addResource(RestApiConstants.FEEDBACK_PATH);
      addResourceMethod(RestApiConstants.HTTP_POST_METHOD, feedback, config.getFeedbackEndpointUrl(),
          authorizer, vcdpEndpointVpcLink, requestParameters, null);
      // add form api
      Map<String, String> formParameters = new HashMap<>(requestIdHeaderParameters);
      formParameters.put("integration.request.path.formId", "method.request.path.formId");
      formParameters.put("integration.request.path.language", "method.request.path.language");

      Map<String, Boolean> formMethodRequestParameters = new HashMap<>();
      formMethodRequestParameters.put("method.request.path.formId", true);
      formMethodRequestParameters.put("method.request.path.language", true);

      Resource form = version.addResource(RestApiConstants.FORM_PATH)
          .addResource(RestApiConstants.FORM_ID_PATH)
          .addResource(RestApiConstants.LANGUAGE_PATH);
      addResourceMethod(RestApiConstants.HTTP_GET_METHOD, form, config.getFormEndpointUrl(),
          authorizer, vcdpEndpointVpcLink, formParameters, formMethodRequestParameters);
    }

    // 26. Addresses

    // 26.1 Find Address
    Resource addresses = version.addResource(RestApiConstants.ADDRESSES_PATH);
    addResourceMethod(RestApiConstants.HTTP_GET_METHOD,
            addresses,
            config.getCustomerApiFindAddressEndpointUrl(),
            authorizer,
            ecoIntEndpointVpcLink,
            requestIdHeaderParameters,
            null);

    // 26.2 Retrieve Address
    Map<String, String> addressIntegrationRequestParameters = new HashMap<>(requestIdHeaderParameters);
    addressIntegrationRequestParameters.put("integration.request.path.addressId", "method.request.path.addressId");

    Map<String, Boolean> addressMethodRequestParameters = new HashMap<>();
    addressMethodRequestParameters.put("method.request.path.addressId", true);

    Resource addressId = addresses.addResource(RestApiConstants.ADDRESS_ID_PATH);
    addResourceMethod(RestApiConstants.HTTP_GET_METHOD,
        addressId,
        config.getCustomerApiRetrieveAddressEndpointUrl(),
        authorizer,
        ecoIntEndpointVpcLink,
        addressIntegrationRequestParameters,
        addressMethodRequestParameters);

    // 27. FAQs
    if (deploymentHelper.isAvailable(OabApiGatewayEnvEnum.CONTENT_SERVICE)) {
      Resource faqs = version.addResource(RestApiConstants.FAQ_PATH);
      addResourceMethod(RestApiConstants.HTTP_GET_METHOD,
              faqs,
              config.getContentManagementServiceEndpointUrl(),
              authorizer,
              ecoIntEndpointVpcLink,
              null,
              null);
    }

    // Devices verifications endpoint in MAB environments
    if (deploymentHelper.isAvailable(NOT_SUPPORT_IN_CN_REGION)
        && deploymentHelper.isAvailable(DELETE_VEHICLE_ENDPOINT)) {
      Resource verifications = version.addResource(RestApiConstants.VERIFICATIONS_PATH);
      addResourceMethod(RestApiConstants.HTTP_POST_METHOD,
              verifications,
              config.getDeviceVerificationServiceEndpointUrl(),
              authorizer,
              mabNginxVpcLink,
              requestIdHeaderParameters,
              null);
    }

    // 28. Guardian Mode Schedule
    Map<String, String> passedGuardianModeParameters = new HashMap<>(requestIdHeaderParameters);
    passedGuardianModeParameters.put("integration.request.header.Authorization", "method.request.header.Authorization");
    passedGuardianModeParameters.put("integration.request.header.Correlation-Id", "context.requestId");

    Map<String, Boolean> requiredGuardianModeParameters = new HashMap<>();
    requiredGuardianModeParameters.put("method.request.header.Authorization", true);
    requiredGuardianModeParameters.put("method.request.header.Correlation-Id", false);

    Map<String, String> passedGuardianModeGetSchedulesParameters = new HashMap<>(passedGuardianModeParameters);
    passedGuardianModeGetSchedulesParameters.put("integration.request.querystring.vuid", "method.request.querystring.vehicle_id");
    passedGuardianModeGetSchedulesParameters.put("integration.request.querystring.status", "method.request.querystring.status");

    Map<String, Boolean> requiredGuardianModeGetSchedulesParameters = new HashMap<>(requiredGuardianModeParameters);
    requiredGuardianModeGetSchedulesParameters.put("method.request.querystring.vehicle_id", true);
    requiredGuardianModeGetSchedulesParameters.put("method.request.querystring.status", false);

    Resource guardianModeSchedule = version.addResource(RestApiConstants.GUARDIAN_MODE_PATH)
            .addResource(RestApiConstants.GUARDIAN_MODE_SCHEDULE_PATH);

    addResourceMethod(
            RestApiConstants.HTTP_GET_METHOD,
            guardianModeSchedule,
            config.getGuardianModeServiceScheduleEndpointUrl(),
            authorizer,
            vcdpEndpointVpcLink,
            passedGuardianModeGetSchedulesParameters,
            requiredGuardianModeGetSchedulesParameters,
            null,
            OabGuardianModeResourceHelper.GET_SCHEDULE_RESPONSE_TEMPLATE
    );

    addResourceMethod(
            RestApiConstants.HTTP_POST_METHOD,
            guardianModeSchedule,
            config.getGuardianModeServiceScheduleEndpointUrl(),
            authorizer,
            vcdpEndpointVpcLink,
            passedGuardianModeParameters,
            requiredGuardianModeParameters,
            OabGuardianModeResourceHelper.POST_SCHEDULE_REQUEST_TEMPLATE,
            OabGuardianModeResourceHelper.POST_SCHEDULE_RESPONSE_TEMPLATE
    );

    Resource guardianModeScheduleById = guardianModeSchedule.addResource(RestApiConstants.ID_PATH);
    passedGuardianModeParameters.put("integration.request.path.id", "method.request.path.id");
    requiredGuardianModeParameters.put("method.request.path.id", true);

    addResourceMethod(
            RestApiConstants.HTTP_PUT_METHOD,
            guardianModeScheduleById,
            config.getGuardianModeServiceScheduleByIdEndpointUrl(),
            authorizer,
            vcdpEndpointVpcLink,
            passedGuardianModeParameters,
            requiredGuardianModeParameters,
            OabGuardianModeResourceHelper.PUT_SCHEDULE_REQUEST_TEMPLATE,
            OabGuardianModeResourceHelper.PUT_SCHEDULE_RESPONSE_TEMPLATE
    );

    addResourceMethod(
            RestApiConstants.HTTP_DELETE_METHOD,
            guardianModeScheduleById,
            config.getGuardianModeServiceScheduleByIdEndpointUrl(),
            authorizer,
            vcdpEndpointVpcLink,
            passedGuardianModeParameters,
            requiredGuardianModeParameters
    );

    // 29. Service Booking
    if (deploymentHelper.isAvailable(OabApiGatewayEnvEnum.SUPPORT_IN_CN_REGION)) {
      final OabServiceBookingResourceHelper sbResource = new OabServiceBookingResourceHelper(deploymentHelper, version, config);
      for (BaseEndpoint baseEndpoint: sbResource.getEndpoints()) {
        addResourceMethodByBaseEndPoint(authorizer, mabNginxVpcLink, baseEndpoint);
      }
    }

    // 30. [CN]Add Vehicle for current user
    if (deploymentHelper.isAvailable(SUPPORT_IN_CN_REGION)) {
      Resource vehicleRecognize = userVehicles.addResource(RestApiConstants.VEHICLE_RECOGNIZE_PATH);
      addResourceMethod(RestApiConstants.HTTP_POST_METHOD, vehicleRecognize,
          config.getAddVehicleServiceRecognizeUrl(),
          authorizer, ecoIntEndpointVpcLink, requestIdHeaderParameters, null);

      Resource vehicleConfirm = userVehicles.addResource(RestApiConstants.VEHICLE_CONFIRM_PATH);
      addResourceMethod(RestApiConstants.HTTP_POST_METHOD, vehicleConfirm,
          config.getAddVehicleServiceConfirmUrl(),
          authorizer, ecoIntEndpointVpcLink, requestIdHeaderParameters, null);
    }

    // 31. Geofence Service
    Map<String, String> passedGeofenceServiceParameters = new HashMap<>();
    passedGeofenceServiceParameters.put("integration.request.header.Authorization", "method.request.header.Authorization");
    Map<String, Boolean> requiredGeofenceServiceParameters = new HashMap<>();
    requiredGeofenceServiceParameters.put("method.request.header.Authorization", true);

    Map<String, String> passedGeofenceServiceGetAllParameters = new HashMap<>(passedGeofenceServiceParameters);
    passedGeofenceServiceGetAllParameters.put("integration.request.querystring.vuid", "method.request.querystring.vehicle_id");
    Map<String, Boolean> requiredGeofenceServiceGetAllParameters = new HashMap<>(requiredGeofenceServiceParameters);
    requiredGeofenceServiceGetAllParameters.put("method.request.querystring.vehicle_id", false);

    Resource geofenceService = version.addResource(RestApiConstants.GEOFENCE_SERVICE_PATH);
    addResourceMethod(
            RestApiConstants.HTTP_GET_METHOD,
            geofenceService,
            config.getGeofenceServiceEndpointUrl(),
            authorizer,
            vcdpEndpointVpcLink,
            passedGeofenceServiceGetAllParameters,
            requiredGeofenceServiceGetAllParameters,
            null,
            OabGeofenceServiceResourceHelper.GET_GEOFENCE_RESPONSE_TEMPLATE
    );

    addResourceMethod(
            RestApiConstants.HTTP_POST_METHOD,
            geofenceService,
            config.getGeofenceServiceEndpointUrl(),
            authorizer,
            vcdpEndpointVpcLink,
            passedGeofenceServiceParameters,
            requiredGeofenceServiceParameters,
            OabGeofenceServiceResourceHelper.POST_GEOFENCE_REQUEST_TEMPLATE,
            OabGeofenceServiceResourceHelper.POST_GEOFENCE_RESPONSE_TEMPLATE
    );

    Map<String, String> passedGeofenceServiceByIdParameters = new HashMap<>(passedGeofenceServiceParameters);
    passedGeofenceServiceByIdParameters.put("integration.request.path.id", "method.request.path.id");
    Map<String, Boolean> requiredGeofenceServiceByIdParameters = new HashMap<>(requiredGeofenceServiceParameters);
    requiredGeofenceServiceByIdParameters.put("method.request.path.id", true);

    Resource geofenceServiceById = geofenceService.addResource(RestApiConstants.ID_PATH);
    addResourceMethod(
            RestApiConstants.HTTP_PUT_METHOD,
            geofenceServiceById,
            config.getGeofenceServiceEndpointUrl() + "/{id}",
            authorizer,
            vcdpEndpointVpcLink,
            passedGeofenceServiceByIdParameters,
            requiredGeofenceServiceByIdParameters,
            OabGeofenceServiceResourceHelper.PUT_GEOFENCE_BY_ID_REQUEST_TEMPLATE,
            OabGeofenceServiceResourceHelper.PUT_GEOFENCE_BY_ID_RESPONSE_TEMPLATE
    );

    addResourceMethod(
            RestApiConstants.HTTP_DELETE_METHOD,
            geofenceServiceById,
            config.getGeofenceServiceEndpointUrl() + "/{id}",
            authorizer,
            vcdpEndpointVpcLink,
            passedGeofenceServiceByIdParameters,
            requiredGeofenceServiceByIdParameters,
            null,
            null
    );
  }

  /**
   * Create a method for the API Gateway Resource by BaseEndpoint.
   */
  private void addResourceMethodByBaseEndPoint(RequestAuthorizer authorizer,
                                               IVpcLink vpcLink,
                                               BaseEndpoint endpoint) {
    addResourceMethod(
        endpoint.getHttpMethod(),
        endpoint.getResource(),
        endpoint.getTargetUrl(),
        authorizer,
        vpcLink,
        endpoint.getParamToBePassed(),
        endpoint.getParamAreRequired()
    );
  }

  /**
   * Create a method for the API Gateway Resource.
   */
  private void addResourceMethod(String httpMethod,
                                 Resource resource,
                                 String endpoint,
                                 RequestAuthorizer authorizer,
                                 IVpcLink vpcLink,
                                 Map<String, String> requestParametersToBePassed,
                                 Map<String, Boolean> requestParametersAreRequired) {
    resource.addMethod(httpMethod,
        OabApiGatewayHelper.createIntegration(httpMethod, endpoint, vpcLink, requestParametersToBePassed),
        getMethodOptions(authorizer, requestParametersAreRequired, null));
  }

  /**
   * Create a method for the API Gateway Resource that allow us to disable http proxy and pass in method response.
   */
  private void addResourceMethod(String httpMethod,
                                 Resource resource,
                                 String endpoint,
                                 RequestAuthorizer authorizer,
                                 IVpcLink vpcLink,
                                 Map<String, String> requestParametersToBePassed,
                                 Map<String, Boolean> requestParametersAreRequired,
                                 Optional<Boolean> httpProxy,
                                 List<MethodResponse> responseList,
                                 List<IntegrationResponse> integrationResponses) {

    // Optional to handle null methodResponses and integrationResponses
    Optional<List<MethodResponse>> responseListOptional = Optional.ofNullable(responseList);
    Optional<List<IntegrationResponse>> integrationResponsesOptional = Optional.ofNullable(integrationResponses);

    resource.addMethod(httpMethod,
        OabApiGatewayHelper.createIntegration(
            httpMethod,
            endpoint,
            vpcLink,
            requestParametersToBePassed,
            httpProxy,
            integrationResponsesOptional.orElse(createDefaultIntegrationResponses())
        ), getMethodOptions(
            authorizer,
            requestParametersAreRequired,
            responseListOptional.orElse(getMethodResponseStatuses())));
  }

  /**
   * Create a method for the API Gateway Resource that allow us to disable http proxy and customize request/response mapping.
   */
  private void addResourceMethod(String httpMethod,
                                 Resource resource,
                                 String endpoint,
                                 RequestAuthorizer authorizer,
                                 IVpcLink vpcLink,
                                 Map<String, String> requestParametersToBePassed,
                                 Map<String, Boolean> requestParametersAreRequired,
                                 String requestTemplate,
                                 String responseTemplate) {
    resource.addMethod(httpMethod,
            OabApiGatewayHelper.createIntegration(httpMethod, endpoint, vpcLink,
                                                  requestParametersToBePassed, requestTemplate, responseTemplate),
            getMethodOptions(authorizer, requestParametersAreRequired,
                             Objects.nonNull(responseTemplate) ? getMethodResponseStatuses() : null));
  }

  /**
   * Create an integration method for retailer search.
   */
  private void addIntegrationMethodForRetailer(Resource resource,
                                               RequestAuthorizer authorizer,
                                               OabApiGatewayAppConfig config) {
    Map<String, Boolean> requestParameters = Map.of("method.request.header.authorization",true);
    List<MethodResponse> responseList = getMethodResponseStatuses();
    resource.addMethod(RestApiConstants.HTTP_POST_METHOD,
        OabApiGatewayHelper.createHttpIntegrationForRetailer(config),
        getMethodOptions(authorizer, requestParameters, responseList));
  }

  private static List<MethodResponse> getMethodResponseStatuses() {
    return List.of(MethodResponse.builder()
            .statusCode("200")
            .build(),
        MethodResponse.builder()
            .statusCode("400")
            .build(),
        MethodResponse.builder()
            .statusCode("401")
            .build(),
        MethodResponse.builder()
            .statusCode("403")
            .build(),
        MethodResponse.builder()
            .statusCode("404")
            .build(),
        MethodResponse.builder()
            .statusCode("500")
            .build()
    );
  }

  /**
   * Create default Integration Responses for the API Gateway Resource.
   */
  private List<IntegrationResponse> createDefaultIntegrationResponses() {
    return List.of(
        IntegrationResponse.builder()
            .selectionPattern("200")
            .statusCode("200")
            .build(),
        IntegrationResponse.builder()
            .selectionPattern("400")
            .statusCode("400")
            .build(),
        IntegrationResponse.builder()
            .selectionPattern("401")
            .statusCode("401")
            .build(),
        IntegrationResponse.builder()
            .selectionPattern("403")
            .statusCode("403")
            .build(),
        IntegrationResponse.builder()
            .selectionPattern("404")
            .statusCode("404")
            .build(),
        IntegrationResponse.builder()
            .selectionPattern("500")
            .statusCode("500")
            .build()
    );
  }

  /**
   * Create an integration method for event.
   */
  private void addLambdaIntegrationMethod(Construct scope,
                                          String httpMethod,
                                          Resource resource,
                                          String functionName,
                                          RequestAuthorizer authorizer,
                                          Model requestModel) {
    resource.addMethod(httpMethod,
        OabApiGatewayHelper.createLambdaIntegration(scope, functionName),
        getMethodOptionsWithRequsetModel(authorizer,requestModel, null));
  }

  /**
   * Create request method options.
   */
  private MethodOptions getMethodOptions(RequestAuthorizer authorizer,
                                         Map<String, Boolean> requestParameters,
                                         List<MethodResponse> responseList) {
    return MethodOptions.builder()
        .authorizer(authorizer)
        .requestParameters(requestParameters)
        .methodResponses(responseList)
        .build();
  }

  /**
   * Create request method options with request model.
   */
  private MethodOptions getMethodOptionsWithRequsetModel(RequestAuthorizer authorizer,
                                                         Model requestModel,
                                                         List<MethodResponse> responseList) {
    return MethodOptions.builder()
        .authorizer(authorizer)
        .requestModels(Map.of("application/json", requestModel))
        .methodResponses(responseList)
        .build();
  }

  /**
   * Create AWS API Gateway.
   */
  private RestApi createRestApi(Construct scope, OabApiGatewayAppConfig config) {
    // Enable CloudWatch for API Gateway
    LogGroup logGroup = OabApiGatewayHelper.createLogGroup(scope,
        RestApiConstants.APIGW_LOG_GROUP_NAME,
        config.getRegion(),
        encryptKeyLogs);
    // Set the log format recommended by DataDog docs: https://docs.datadoghq.com/integrations/amazon_api_gateway/
    Map<String, Object> logFormatMap = new HashMap<>(Map.of(
        "apiId", AccessLogField.contextApiId(),
        "stage", AccessLogField.contextStage(),
        "requestId", AccessLogField.contextRequestId(),
        "ip", AccessLogField.contextIdentitySourceIp(),
        "caller", AccessLogField.contextIdentityCaller(),
        "user", AccessLogField.contextIdentityUser(),
        "requestTime", AccessLogField.contextRequestTimeEpoch(),
        "httpMethod", AccessLogField.contextHttpMethod(),
        "resourcePath", AccessLogField.contextResourcePath(),
        "status", AccessLogField.contextStatus()
    ));
    logFormatMap.putAll(Map.of(
        "protocol", AccessLogField.contextProtocol(),
        "responseLength", AccessLogField.contextResponseLength(),
        "identity", Map.of("userAgent", AccessLogField.contextIdentityUserAgent())
    ));
    AccessLogFormat accessLogFormat = AccessLogFormat.custom((new JSONObject(logFormatMap)).toString());

    StageOptions stageOptions = StageOptions.builder()
        .stageName(config.getDeploymentStage())
        .cachingEnabled(false)
        .accessLogDestination(new LogGroupLogDestination(logGroup))
        .accessLogFormat(accessLogFormat)
        .loggingLevel(MethodLoggingLevel.INFO)
        .metricsEnabled(true)
        .tracingEnabled(false)
        .build();

    // API Gateway
    return new RestApi(this, RestApiConstants.APIGW_NAME,
        RestApiProps.builder()
            .restApiName(RestApiConstants.APIGW_NAME)
            .cloudWatchRole(true)
            .deployOptions(stageOptions)
            .description(RestApiConstants.APIGW_DESC)
            .binaryMediaTypes(List.of(RestApiConstants.MULTIPART_FORM_DATA))
            .endpointTypes(List.of(EndpointType.REGIONAL))
            .build());
  }

  /**
   * Create ForgeRock Lambda Authorizer for API Gateway.
   */
  private RequestAuthorizer createFrLambdaAuthorizer(IFunction function) {

    // API Gateway Authorizer
    return new RequestAuthorizer(this, RestApiConstants.APIGW_AUTHORIZER_NAME,
        RequestAuthorizerProps.builder()
            .resultsCacheTtl(Duration.minutes(0))
            .authorizerName(RestApiConstants.APIGW_AUTHORIZER_NAME)
            .handler(function)
            .identitySources(List.of(IdentitySource.header("Authorization")))
            .build());
  }

  /**
   * Create Lambda function for authorizer.
   */
  private IFunction getAuthorizerFunction(OabApiGatewayAppConfig config,
                                          IVpc lambdaVpc,
                                          SecurityGroup lambdaSg,
                                          IQueue deadLetterQueue,
                                          CfnCacheCluster cacheCluster,
                                          LogGroup lambdaAuthorizerLogGroup,
                                          OabDeploymentHelper deploymentHelper) {

    // Environment variables for Datadog, ForgeRock and MemCached
    Map<String, String> environment = getEnvironmentVariablesMap(config, cacheCluster);

    Function authorizerFunction = OabApiGatewayHelper.createLambdaFunction(this, RestApiConstants.LAMBDA_AUTHORIZER_FUNC_NAME,
        "../software/oabtokenauthorizer/target/oab-token-authorizer-software-1.0.jar",
        "com.jaguarlandrover.d9.oneappbackend.lambda.OabTokenLambdaAuthorizer",
        Runtime.JAVA_17,
        lambdaVpc,
        lambdaSg,
        deadLetterQueue,
        environment,
        lambdaAuthorizerLogGroup,
        config,
        deploymentHelper);

    List<String> listOfResources = new ArrayList<>();
    listOfResources.add(config.getApproovSecretArn());
    listOfResources.add(config.getDdSecretArn());

    // Adding permissions to the lambda function role to get access to the dd secret key
    authorizerFunction.addToRolePolicy(new PolicyStatement(PolicyStatementProps.builder()
        .effect(Effect.ALLOW)
        .actions(List.of("secretsmanager:GetResourcePolicy", "secretsmanager:GetSecretValue",
            "secretsmanager:DescribeSecret", "secretsmanager:ListSecretVersionIds"))
        .resources(listOfResources)
        .build()));

    // Enable Function Version and SnapStart
    CfnFunction cfnFunction = (CfnFunction) authorizerFunction.getNode().getDefaultChild();
    if (cfnFunction != null) {
      cfnFunction.addPropertyOverride("SnapStart", Map.of("ApplyOn", "PublishedVersions"));
    }

    return authorizerFunction.getCurrentVersion();
  }

  private Map<String, String> getEnvironmentVariablesMap(OabApiGatewayAppConfig config, CfnCacheCluster cacheCluster) {
    Map<String, String> environmentVariablesMap = new HashMap<>(Map.ofEntries(
        entry("DD_CAPTURE_LAMBDA_PAYLOAD", FALSE),
        entry("DD_MERGE_XRAY_TRACES", TRUE),
        entry("FORGEROCK_HOST", config.getForgeRockHost()),
        entry("POLICY_ARN_FORMAT", config.getPolicyArnFormat()),
        entry("APPROOV_ENABLED", config.getApproovEnabled()),
        entry("CACHE_ENDPOINT", cacheCluster.getAttrConfigurationEndpointAddress()),
        entry("CACHE_PORT", cacheCluster.getAttrConfigurationEndpointPort()),
        entry("AWS_LAMBDA_EXEC_WRAPPER", config.getDdWrapper()),
        entry("DD_SITE", config.getDdSite()),
        entry("DD_API_KEY_SECRET_ARN", config.getDdSecretArn())
    ));
    environmentVariablesMap.putAll(lambdaEnvironmentVariables);
    return environmentVariablesMap;
  }

  private LambdaIntegration createSnapStartIntegration(Construct scope,
                                                       Function function,
                                                       String pathToSource,
                                                       String id,
                                                       OabDeploymentHelper deploymentHelper) {

    ((CfnFunction) function.getNode().getDefaultChild()).setSnapStart(
        CfnFunction.SnapStartProperty.builder().applyOn("PublishedVersions").build());

    // we need a unique name for each time the code is changed
    String codeSha256 = CodeHash.fileDirectoryJarAndPomSha256ToBase64(pathToSource);

    Version lambdaVersion = Version.Builder.create(scope, id + "-Version-" + codeSha256.substring(0, 6))
        .lambda(function)
        .build();

    Alias alias = Alias.Builder.create(scope, function.getNode().getId() + "-snapstart-alias")
        .aliasName("snapstart-alias").version(lambdaVersion).build();

    return LambdaIntegration.Builder.create(alias).build();
  }

  private Function createRouterHandlerFunction(String stackId,
                                               IVpc vpc, SecurityGroup securityGroup,
                                               SubnetSelection subnetSelection,
                                               Map<String, String> environmentVariables) {

    FunctionProps functionProps = FunctionProps.builder().environment(environmentVariables)
        .functionName("oab-commands-request-handler-proxy")
        .handler("com.jaguarlandrover.d9.oneappbackend.lambda.proxy.OabCommandsRequestHandlerProxy::handleRequest")
        .code(Code.fromAsset("../software/commandsproxyhandler/target/commands-proxy-handler-1.0.jar"))
        .memorySize(2048)
        .timeout(Duration.seconds(LAMBDA_CONFIG_TIMEOUT_IN_SECONDS))
        .securityGroups(List.of(securityGroup))
        .vpc(vpc)
        .vpcSubnets(subnetSelection)
        .runtime(Runtime.JAVA_17).build();

    return new Function(this, stackId + "-oab-commands-request-handler-proxy", functionProps);
  }
}
