/*
 * Copyright (c) Jaguar Land Rover Ltd 2023. All rights reserved
 */

package com.jaguarlandrover.d9.oneappbackend.util;

public class OabGeofenceServiceResourceHelper {

  private OabGeofenceServiceResourceHelper() {
  }

  public static final String GET_GEOFENCE_RESPONSE_TEMPLATE = """
          #set($geofences = $input.path('$'))
          [
            #foreach($geofence in $geofences)
            {
                "id": "$geofence.id",
                "name": "$geofence.name",
                "type": "$geofence.type",
                "vehicle_id": "$geofence.vuid",
                "area": {
                   "latitude": $geofence.area.latitude,
                   "longitude": $geofence.area.longitude,
                   "radius": $geofence.area.radius
                }
            }#if($foreach.hasNext),#end
            #end
          ]
          """;

  public static final String POST_GEOFENCE_REQUEST_TEMPLATE = """
          #set($geofence = $input.path('$'))
          {
              "name": "$geofence.name",
              "type": "USER",
              "area": {
                 "latitude": $geofence.area.latitude,
                 "longitude": $geofence.area.longitude,
                 "radius": $geofence.area.radius
               },
              "vuid": "$geofence.vehicle_id"
          }
          """;

  public static final String POST_GEOFENCE_RESPONSE_TEMPLATE = """
          #set($geofence = $input.path('$'))
          {
              "name": "$geofence.name",
              "type": "USER",
              "area": {
                 "latitude": $geofence.area.latitude,
                 "longitude": $geofence.area.longitude,
                 "radius": $geofence.area.radius
               },
              "vehicle_id": "$geofence.vuid",
              "id": "$geofence.id"
          }
          """;

  public static final String PUT_GEOFENCE_BY_ID_REQUEST_TEMPLATE = """
          #set($geofence = $input.path('$'))
          {
              "name": "$geofence.name",
              "type": "USER",
              "area": {
                 "latitude": $geofence.area.latitude,
                 "longitude": $geofence.area.longitude,
                 "radius": $geofence.area.radius
               },
              "vuid": "$geofence.vehicle_id"
          }
          """;

  public static final String PUT_GEOFENCE_BY_ID_RESPONSE_TEMPLATE = """
          #set($geofence = $input.path('$'))
          {
              "name": "$geofence.name",
              "type": "$geofence.type",
              "area": {
                 "latitude": $geofence.area.latitude,
                 "longitude": $geofence.area.longitude,
                 "radius": $geofence.area.radius
               },
              "vehicle_id": "$geofence.vuid",
              "id": "$geofence.id"
          }
          """;
}