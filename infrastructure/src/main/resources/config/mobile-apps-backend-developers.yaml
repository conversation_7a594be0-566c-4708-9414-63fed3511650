#
# API Gateway Configurations
#
region: eu-west-2
deploymentStage: dev
#
# Lambda VPC Configurations
vpcName: mobile-apps-backend-developers-vpc
vpcId: vpc-024a7030a3ef2a58e
subnetIds:
  - subnet-0c1e0f6b6d810e1bb
  - subnet-0b3464e3fcaf42d4e
  - subnet-036db695028eef78f
availabilityZones:
  - eu-west-2a
  - eu-west-2b
  - eu-west-2c
#
# AWS Accounts
#
account: ************ # nosemgrep: generic.secrets.security.detected-aws-account-id.detected-aws-account-id
#
# VPC Link
#
#
nginxNlbArn: arn:aws:elasticloadbalancing:eu-west-2:************:loadbalancer/net/eks-nginx-ingress-service/e8d67526099bfadf
apigwVpcEndpoint: execute-api
#
# ForgeRock Server
#
forgeRockHost: https://qa.identity.jaguarlandrover.com
#
# policyArnFormat Configurations
#
policyArnFormat: arn:aws:execute-api:%s:%s:%s/%s/%s/%s
#
# Approov
#
approovEnabled: false
approovSecretArn: arn:aws:secretsmanager:eu-west-2:************:secret:oab/approov-6RdC3p
#
# Custom Domain Name
#
certificate: arn:aws:acm:eu-west-2:************:certificate/97d439d3-9602-4196-8f61-01caddae4a8d
customName: oneapp
domainName: mab.dev.jlr-vcdp.com
hostedZoneId: Z0195728201FAOFWU7CAA
privateHostedZoneId: Z0365406Y8HRDB7EWRPH
#
# DDA-104438
# Create a 2nd Custom Domain for domain switch
# Presence of these will be detected in the domain function
#
certificateV2: arn:aws:acm:eu-west-2:************:certificate/b72c12e7-b8e1-4d93-978d-67e7a85e1f5b
domainNameV2: mab-dev.jlr-vcdp.com
hostedZoneIdV2: Z03914743SGTCM62KA2RF
privateHostedZoneIdV2: Z029138332PQWPD0Y6HOH

#
# Memcached
#
cacheNodeCount: 3
#
# Command Router
#
commandRouterUrl: https://8bn38d90w4.execute-api.eu-west-2.amazonaws.com/dev
#
# Place Service
placeServiceUrl: https://place-gateway.dev.jlr-vcdp.com
#
# Scheduling Service
#
scheduleServiceUrl: https://qi2jdk5dki.execute-api.eu-west-2.amazonaws.com/vcdp-dev/{vehicleId}/schedules
#
# Guardian Mode Service
#
guardianModeServiceScheduleEndpointUrl: https://zovodp5rp0.execute-api.eu-west-2.amazonaws.com/dev/api/v1/schedule
guardianModeServiceScheduleByIdEndpointUrl: https://zovodp5rp0.execute-api.eu-west-2.amazonaws.com/dev/api/v1/schedule/{id}
#
# Event Lambda Function Service
eventLambdaFunctionName: one-app-backend-event-lambda-function
#
# Notification Lambda Function Service
notificationLambdaFunctionName: oab-notification-registration-lambda
#
# Trip Service In Eco-Int
#
tripServiceMeEndpointUrl: https://uy33ews0xh.execute-api.eu-west-1.amazonaws.com/dev/api/v1/users/me/vehicles/{vehicleId}/trips
tripDetailsServiceMeEndpointUrl: https://uy33ews0xh.execute-api.eu-west-1.amazonaws.com/dev/api/v1/users/me/vehicles/{vehicleId}/trips/{tripId}
#
# Content Management Integration Service In Eco-Int
#
contentManagementServiceEndpointUrl: https://zcelcbv1o5.execute-api.eu-west-1.amazonaws.com/dev/api/v1/faqs
#
# Loqate Service In Eco-Int
#
customerApiFindAddressEndpointUrl: https://z99regwomc.execute-api.eu-west-1.amazonaws.com/dev/api/v1/addresses
customerApiRetrieveAddressEndpointUrl: https://z99regwomc.execute-api.eu-west-1.amazonaws.com/dev/api/v1/addresses/{addressId}
#
# PaaK
#
paakOwnerPairingEndpointUrl: https://xrmz85y1bc.execute-api.eu-west-2.amazonaws.com/api/v1/users/me/paak/vehicles/{vehicleId}/pairing
paakSuspendKeyEndpointUrl: https://xrmz85y1bc.execute-api.eu-west-2.amazonaws.com/api/v1/users/me/paak/vehicles/{vehicleId}/keys/{slotId}/suspend
paakResumeKeyEndpointUrl: https://xrmz85y1bc.execute-api.eu-west-2.amazonaws.com/api/v1/users/me/paak/vehicles/{vehicleId}/keys/{slotId}/resume
paakGetPhysicalKeysEndpointUrl: https://xrmz85y1bc.execute-api.eu-west-2.amazonaws.com/api/v1/users/me/paak/vehicles/{vehicleId}/keys/physical
paakVehicleIdentityMappingEndpointUrl: https://xrmz85y1bc.execute-api.eu-west-2.amazonaws.com/api/v1/users/me/paak/vehicles/identity-mapping
#
# Datadog configurations
#
ddEnv: mobile-apps-backend-developers
ddService: oab-api-gw
ddManagedBy: cdk
ddPiiDataHandler: false
ddProductOwner: <EMAIL>
ddRepoUrl: https://git-gdd.sdo.jlrmotor.com/D9/one-app/api-gateway/oab-api-gateway-cdk
ddSquad: pineapple
ddServiceLambdaAuthorizer: oab-api-gw-lambda-authorizer
ddWrapper: /opt/datadog_wrapper
ddSite: datadoghq.eu
ddSecretArn: arn:aws:secretsmanager:eu-west-2:************:secret:DdApiKeySecret-LN8QdbIwjyAK-oxTGao
#
# Datadog trace and extension arn version
#
ddExtension: Datadog-Extension
ddExtensionVersion: arn:aws:lambda:eu-west-2:464622532012:layer:Datadog-Extension:55
ddTraceJava: DD-trace-java
ddTraceJavaVersion: arn:aws:lambda:eu-west-2:464622532012:layer:dd-trace-java:14
lambdaInsightsArn: arn:aws:lambda:eu-west-2:580247275435:layer:LambdaInsightsExtension:45
#
# Endpoint URLs for MAB EKS services
# DDA-104438
# Will need these a 2nd time for private hosted zone at a later date
#
userServiceUsersEndpointUrl: https://oab-user-service.mab.dev.jlr-vcdp.com/api/v1/users
userServiceUsersRegisterEndpointUrl: https://oab-user-service.mab.dev.jlr-vcdp.com/api/v1/users/register
userServiceUsersUpdateEndpointUrl: https://oab-user-service.mab.dev.jlr-vcdp.com/api/v1/users/update
userServiceUsersDeleteEndpointUrl: https://oab-user-service.mab.dev.jlr-vcdp.com/api/v1/users/delete
userServiceUsersRetrieveConsentsEndpointUrl: https://oab-user-service.mab.dev.jlr-vcdp.com/api/v1/users/consents
userServiceUsersUpdateConsentsEndpointUrl: https://oab-user-service.mab.dev.jlr-vcdp.com/api/v1/users/consents
featureServiceMeEndpointUrl: https://oab-feature-service.mab.dev.jlr-vcdp.com/api/v1/users/{id}/features
vehicleConsentsServiceEndpointUrl: https://oab-vehicle-consents-service.mab.dev.jlr-vcdp.com/api/v1/vehicles/{vehicleId}/consents
vehicleServiceMyVehiclesEndpointUrl: https://oab-vehicle-service.mab.dev.jlr-vcdp.com/api/v1/users/me/vehicles
vehicleServiceVehicleByIdEndpointUrl: https://oab-vehicle-service.mab.dev.jlr-vcdp.com/api/v1/users/me/vehicles/{vehicleId}
homeChargingServiceChargersEndpointUrl: https://oab-homecharging-service.mab.dev.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>
homeChargingServiceChargersStartEndpointUrl: https://oab-homecharging-service.mab.dev.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/start
homeChargingServiceChargersStopEndpointUrl: https://oab-homecharging-service.mab.dev.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/stop
homeChargingServiceChargersStatusEndpointUrl: https://oab-homecharging-service.mab.dev.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/charge-point-status
homeChargingServiceChargerByIdEndpointUrl: https://oab-homecharging-service.mab.dev.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}
homeChargingServiceChargerByIdDefaultScheduleEndpointUrl: https://oab-homecharging-service.mab.dev.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/schedules/default
homeChargingServiceChargerTariffsEndpointUrl: https://oab-homecharging-service.mab.dev.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/tariffs
homeChargingServiceChargerHistoryEndpointUrl: https://oab-homecharging-service.mab.dev.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/history
homeChargingServiceChargerHistoryLastEndpointUrl: https://oab-homecharging-service.mab.dev.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/history/last
homeChargingServiceChargerHistoryCsvEndpointUrl: https://oab-homecharging-service.mab.dev.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/history/csv
publicChargingServiceUrl: https://oab-public-charging-service.mab.dev.jlr-vcdp.com/api/v1/charging/public/stations
publicChargingServiceGetFaqsEndpointUrl: https://oab-public-charging-service.mab.dev.jlr-vcdp.com/api/v1/charging/public/faqs
publicChargingServiceGetFiltersEndpointUrl: https://oab-public-charging-service.mab.dev.jlr-vcdp.com/api/v1/charging/public/filters
publicChargingServiceGetKeywordsEndpointUrl: https://oab-public-charging-service.mab.dev.jlr-vcdp.com/api/v1/charging/public/trending-keywords
publicChargingServiceGetConnectorDetailsEndpointUrl: https://oab-public-charging-service.mab.dev.jlr-vcdp.com/api/v1/charging/public/stations/{stationId}/connector-detail
publicChargingServiceChargersStartEndpointUrl: https://oab-public-charging-service.mab.dev.jlr-vcdp.com/api/v1/charging/public/command/sessions/{sessionId}/start
publicChargingServiceChargersStopEndpointUrl: https://oab-public-charging-service.mab.dev.jlr-vcdp.com/api/v1/charging/public/command/sessions/{sessionId}/stop
publicChargingServiceGetChargerDetailsEndpointUrl: https://oab-public-charging-service.mab.dev.jlr-vcdp.com/api/v1/charging/public/charge-history/sessions/charge/{sessionId}
publicChargingServiceGetOccupancyDetailsEndpointUrl: https://oab-public-charging-service.mab.dev.jlr-vcdp.com/api/v1/charging/public/charge-history/sessions/occupancy/{sessionId}
publicChargingServiceGetUnfinishedOrderEndpointUrl: https://oab-public-charging-service.mab.dev.jlr-vcdp.com/api/v1/charging/public/charge-history/sessions
publicChargingServiceGetChargingHistoryEndpointUrl: https://oab-public-charging-service.mab.dev.jlr-vcdp.com/api/v1/charging/public/charge-history/{filter}
publicChargingServiceGetChargingSessionHistoryEndpointUrl: https://oab-public-charging-service.mab.dev.jlr-vcdp.com/api/v1/charging/public/charge-history/{filter}/sessions
publicChargingServiceGetJlrContractsServiceEndpointUrl: https://oab-public-charging-service.mab.dev.jlr-vcdp.com/api/v1/users/me/contracts
preferenceServiceMyPreferencesEndpointUrl: https://oab-preference-service.mab.dev.jlr-vcdp.com/api/v1/users/me/preferences
deviceVerificationServiceEndpointUrl: https://oab-device-verification-service.mab.dev.jlr-vcdp.com/api/v1/verifications
#
# Vehicle Position Service In Eco-Int
#
vehiclePositionServiceMeEndpointUrl: https://uy33ews0xh.execute-api.eu-west-1.amazonaws.com/dev/api/v1/users/me/vehicles/{vehicleId}/positions/latest
vehicleParkedPositionServiceMeEndpointUrl: https://uy33ews0xh.execute-api.eu-west-1.amazonaws.com/dev/api/v1/users/me/vehicles/{vehicleId}/positions/parked
#
# Service Booking Services In Eco-Int
#
serviceBookingSubmitUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings
serviceBookingDetailsUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings/{bookingNo}
serviceBookingCancelUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings/{bookingNo}
serviceBookingListUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings/query
serviceBookingFileUploadUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings/upload
serviceBookingLastDealerUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/dealers/last
serviceBookingServiceAdvisorsUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/dealers/{dealerNo}/advisors
serviceBookingDealerScheduleUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/dealers/{dealerNo}/schedules
#
# Geofence Service
#
geofenceServiceEndpointUrl: https://sslbsy59a0.execute-api.eu-west-2.amazonaws.com/v1/api/v1/geofence
#