#
# API Gateway Configurations
#
region: cn-northwest-1
deploymentStage: cn-preprod
#
# Lambda VPC Configurations
#
vpcName: mab-cn-preprod
vpcId: vpc-063a89aeb78363845
subnetIds:
  - subnet-0e47ec12fa58a0c4b
  - subnet-0e00e53397524a7b9
  - subnet-07575a96492e39ea4
availabilityZones:
  - cn-northwest-1a
  - cn-northwest-1b
  - cn-northwest-1c
#
# AWS Accounts
#
account: ************ # nosemgrep: generic.secrets.security.detected-aws-account-id.detected-aws-account-id
#
# VPC Link
#
nginxNlbArn: arn:aws-cn:elasticloadbalancing:cn-northwest-1:************:loadbalancer/net/eks-nginx-ingress-service/e6394966bbec238f
apigwVpcEndpoint: execute-api
#
# ForgeRock Server
#
forgeRockHost: https://int.identity.jaguarlandrover.cn
#
# policyArnFormat Configurations
#
policyArnFormat: arn:aws-cn:execute-api:%s:%s:%s/%s/%s/%s
#
# Approov
#
approovEnabled: false
approovSecretArn: arn:aws-cn:secretsmanager:cn-northwest-1:************:secret:oab/approov-tgaWPr
#
# Custom Domain Name
#
certificate: arn:aws-cn:acm:cn-northwest-1:************:certificate/db62cc8b-7fd7-470f-ace2-f25bf5ed6335
customName: oneapp
domainName: mab.cn-preprod.jlr-vcdp.dcclouds.com
hostedZoneId: Z0980168KFDPWGVFSI1D
privateHostedZoneId: Z0999280297SYP3O99RO5
#
# DDA-104438
# Create a 2nd Custom Domain for domain switch
# Presence of these will be detected in the domain function
#
certificateV2:
domainNameV2: mab.cn-preprod.jlr-vcdp.dcclouds.com
hostedZoneIdV2:
privateHostedZoneIdV2:
#
# Memcached
#
cacheNodeCount: 3
#
# Command Router
#
commandRouterUrl: https://jzru4ft6ci.execute-api.cn-northwest-1.amazonaws.com.cn/cn-preprod
#
# Place Service
placeServiceUrl: https://place-gateway.dev.jlr-vcdp.com
#
# Scheduling Service
#
scheduleServiceUrl: https://qi2jdk5dki.execute-api.eu-west-2.amazonaws.com/vcdp-developers/{vehicleId}/schedules
#
# Guardian Mode Service
#
guardianModeServiceScheduleEndpointUrl: https://xfz9b3vlr3.execute-api.eu-west-2.amazonaws.com/pre-prod/api/v1/schedule
guardianModeServiceScheduleByIdEndpointUrl: https://xfz9b3vlr3.execute-api.eu-west-2.amazonaws.com/pre-prod/api/v1/schedule/{id}
#
# Event Lambda Function Service
eventLambdaFunctionName: one-app-backend-event-lambda-function
#
# Notification Lambda Function Service
notificationLambdaFunctionName: oab-notification-registration-lambda
#
# Trip Service In Eco-Int
#
tripServiceMeEndpointUrl: https://oab-trip-service.mab.dev.jlr-vcdp.com/api/v1/users/me/vehicles/{vehicleId}/trips
tripDetailsServiceMeEndpointUrl: https://oab-trip-service.mab.dev.jlr-vcdp.com/api/v1/users/me/vehicles/{vehicleId}/trips/{tripId}
#
# Content Management Integration Service In Eco-Int
#
contentManagementServiceEndpointUrl: https://dkhiyma0u0.execute-api.cn-northwest-1.amazonaws.com.cn/cn-preprod/api/v1/faqs
#
# Loqate Service In Eco-Int
#
customerApiFindAddressEndpointUrl: https://sn5dovyxc2.execute-api.eu-west-1.amazonaws.com/pre-production/api/v1/addresses
customerApiRetrieveAddressEndpointUrl: https://sn5dovyxc2.execute-api.eu-west-1.amazonaws.com/pre-production/api/v1/addresses/{addressId}
#
# Datadog configurations
#
ddEnv: mobile-apps-backend-cn-preprod
ddService: oab-api-gw
ddManagedBy: cdk
ddPiiDataHandler: false
ddProductOwner: <EMAIL>
ddRepoUrl: https://git-gdd.sdo.jlrmotor.com/D9/one-app/api-gateway/oab-api-gateway-cdk
ddSquad: pineapple
ddServiceLambdaAuthorizer: oab-api-gw-lambda-authorizer
ddWrapper: /opt/datadog_wrapper
ddSite: datadoghq.eu
ddSecretArn: arn:aws-cn:secretsmanager:cn-northwest-1:************:secret:datadog-forward-lambda-apikey-PL5Zqa
#
# Datadog trace and extension arn version
#
ddExtension: Datadog-Extension
ddExtensionVersion: arn:aws-cn:lambda:cn-northwest-1:068946021388:layer:datadog-extension-55:1
ddTraceJava: DD-trace-java
ddTraceJavaVersion: arn:aws-cn:lambda:cn-northwest-1:068946021388:layer:datadog-trace-java-14:1
lambdaInsightsArn: arn:aws-cn:lambda:cn-northwest-1:488211338238:layer:LambdaInsightsExtension:43
#
# Endpoint URLs for EKS services
#
userServiceUsersEndpointUrl: https://71y7w9vy59.execute-api.cn-northwest-1.amazonaws.com.cn/cn-preprod/api/v1/users
userServiceUsersRegisterEndpointUrl: https://71y7w9vy59.execute-api.cn-northwest-1.amazonaws.com.cn/cn-preprod/api/v1/users/register
userServiceUsersUpdateEndpointUrl: https://71y7w9vy59.execute-api.cn-northwest-1.amazonaws.com.cn/cn-preprod/api/v1/users/update
userServiceUsersDeleteEndpointUrl: https://oab-user-service.mab.pre-prod.jlr-vcdp.com/api/v1/users/delete
userServiceUsersAvatarEndpointUrl: https://71y7w9vy59.execute-api.cn-northwest-1.amazonaws.com.cn/cn-preprod/api/v1/users/me/avatar
userServiceUsersRetrieveConsentsEndpointUrl: https://71y7w9vy59.execute-api.cn-northwest-1.amazonaws.com.cn/cn-preprod/api/v1/users/consents
userServiceUsersUpdateConsentsEndpointUrl: https://71y7w9vy59.execute-api.cn-northwest-1.amazonaws.com.cn/cn-preprod/api/v1/users/consents
featureServiceMeEndpointUrl: https://oab-feature-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/users/{id}/features
vehicleConsentsServiceEndpointUrl: https://oab-vehicle-consents-service.mab.cn-preprod.jlr-vcdp.com/api/v1/vehicles/{vehicleId}/consents
vehicleServiceMyVehiclesEndpointUrl: https://oab-vehicle-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/users/me/vehicles
vehicleServiceVehicleByIdEndpointUrl: https://oab-vehicle-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/users/me/vehicles/{vehicleId}
homeChargingServiceChargersEndpointUrl: https://oab-homecharging-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/users/me/charging/home/<USER>
homeChargingServiceChargersStartEndpointUrl: https://oab-homecharging-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/users/me/charging/home/<USER>/{chargerId}/start
homeChargingServiceChargersStopEndpointUrl: https://oab-homecharging-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/users/me/charging/home/<USER>/{chargerId}/stop
homeChargingServiceChargersStatusEndpointUrl: https://oab-homecharging-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/users/me/charging/home/<USER>/{chargerId}/charge-point-status
homeChargingServiceChargerByIdEndpointUrl: https://oab-homecharging-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/users/me/charging/home/<USER>/{chargerId}
homeChargingServiceChargerByIdDefaultScheduleEndpointUrl: https://oab-homecharging-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/users/me/charging/home/<USER>/{chargerId}/schedules/default
homeChargingServiceChargerTariffsEndpointUrl: https://oab-homecharging-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/users/me/charging/home/<USER>/{chargerId}/tariffs
homeChargingServiceChargerHistoryEndpointUrl: https://oab-homecharging-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/users/me/charging/home/<USER>/{chargerId}/history
homeChargingServiceChargerHistoryLastEndpointUrl: https://oab-homecharging-service.mab.cn-preprod.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/history/last
homeChargingServiceChargerHistoryCsvEndpointUrl: https://oab-homecharging-service.mab.cn-preprod.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/history/csv
publicChargingServiceUrl: https://oab-charging-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/charging/public/stations
publicChargingServiceGetFaqsEndpointUrl: https://oab-charging-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/charging/public/faqs
publicChargingServiceGetFiltersEndpointUrl: https://oab-charging-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/charging/public/filters
publicChargingServiceGetKeywordsEndpointUrl: https://oab-charging-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/charging/public/trending-keywords
publicChargingServiceGetConnectorDetailsEndpointUrl: https://oab-charging-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/charging/public/stations/{stationId}/connector-detail
publicChargingServiceChargersStartEndpointUrl: https://oab-charging-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/charging/public/command/sessions/{sessionId}/start
publicChargingServiceChargersStopEndpointUrl: https://oab-charging-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/charging/public/command/sessions/{sessionId}/stop
publicChargingServiceGetChargerDetailsEndpointUrl: https://oab-charging-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/charging/public/charge-history/sessions/charge/{sessionId}
publicChargingServiceGetOccupancyDetailsEndpointUrl: https://oab-charging-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/charging/public/charge-history/sessions/occupancy/{sessionId}
publicChargingServiceGetUnfinishedOrderEndpointUrl: https://oab-charging-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/charging/public/charge-history/sessions
publicChargingServiceGetChargingHistoryEndpointUrl: https://oab-charging-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/charging/public/charge-history/{filter}
publicChargingServiceGetChargingSessionHistoryEndpointUrl: https://oab-charging-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/charging/public/charge-history/{filter}/sessions
publicChargingServiceGetJlrContractsServiceEndpointUrl: https://oab-public-charging-service.mab.dev.jlr-vcdp.com/api/v1/vehicles/contracts
preferenceServiceMyPreferencesEndpointUrl: https://oab-preference-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/users/me/preferences
deviceVerificationServiceEndpointUrl: https://oab-device-verification-service.mab.pre-prod.jlr-vcdp.com/api/v1/verifications
feedbackEndpointUrl: https://0j4k3mrf82.execute-api.cn-northwest-1.amazonaws.com.cn/cn-preprod/api/v1/feedback
formEndpointUrl: https://0j4k3mrf82.execute-api.cn-northwest-1.amazonaws.com.cn/cn-preprod/api/v1/form/{formId}/{language}
retailerServiceEndpointUrl: https://rqruognv2f.execute-api.cn-northwest-1.amazonaws.com.cn/cn-preprod/api/v1/retailers
retailerApiServiceTypesEndpointUrl: https://rqruognv2f.execute-api.cn-northwest-1.amazonaws.com.cn/cn-preprod/api/v1/retailers/service-types
retailerApiCitiesEndpointUrl: https://rqruognv2f.execute-api.cn-northwest-1.amazonaws.com.cn/cn-preprod/api/v1/retailers/cities
retailerApiFavoriteEndpointUrl: https://rqruognv2f.execute-api.cn-northwest-1.amazonaws.com.cn/cn-preprod/api/v1/retailers/{tradingCode}/favorite
notificationIntegrationServiceUrl: https://0p664plhk2.execute-api.cn-northwest-1.amazonaws.com.cn/cn-preprod/api/v1/users/me/devices

#
# Vehicle Position Service In Eco-Int
#
vehiclePositionServiceMeEndpointUrl: https://9rlw8x5m3b.execute-api.eu-west-1.amazonaws.com/pre-production/api/v1/users/me/vehicles/{vehicleId}/positions/latest
vehicleParkedPositionServiceMeEndpointUrl: https://9rlw8x5m3b.execute-api.eu-west-1.amazonaws.com/pre-production/api/v1/users/me/vehicles/{vehicleId}/positions/parked
#
# PaaK
#
paakOwnerPairingEndpointUrl: https://xrmz85y1bc.execute-api.eu-west-2.amazonaws.com/api/v1/users/me/paak/vehicles/{vehicleId}/pairing
paakSuspendKeyEndpointUrl: https://xrmz85y1bc.execute-api.eu-west-2.amazonaws.com/api/v1/users/me/paak/vehicles/{vehicleId}/keys/{slotId}/suspend
paakResumeKeyEndpointUrl: https://xrmz85y1bc.execute-api.eu-west-2.amazonaws.com/api/v1/users/me/paak/vehicles/{vehicleId}/keys/{slotId}/resume
paakGetPhysicalKeysEndpointUrl: https://xrmz85y1bc.execute-api.eu-west-2.amazonaws.com/api/v1/users/me/paak/vehicles/{vehicleId}/keys/physical
#
# Service Booking Services In Eco-Int
#
serviceBookingSubmitUrl: https://oab-service-booking-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings
serviceBookingDetailsUrl: https://oab-service-booking-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings/{bookingNo}
serviceBookingCancelUrl: https://oab-service-booking-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings/{bookingNo}
serviceBookingListUrl: https://oab-service-booking-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings/query
serviceBookingFileUploadUrl: https://oab-service-booking-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings/upload
serviceBookingLastDealerUrl: https://oab-service-booking-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/service-booking/dealers/last
serviceBookingServiceAdvisorsUrl: https://oab-service-booking-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/service-booking/dealers/{dealerNo}/advisors
serviceBookingDealerScheduleUrl: https://oab-service-booking-bff-service.mab.cn-preprod.jlr-vcdp.dcclouds.com/api/v1/service-booking/dealers/{dealerNo}/schedules
#
# [CN]Add Vehicle Service In Eco-Int
#
# upload IDCard/VehicleLicense/VehicleInvoice for current user
addVehicleServiceRecognizeUrl: https://6l7s44jzcl.execute-api.cn-northwest-1.amazonaws.com.cn/cn-preprod/api/v1/vehicle/recognize
# bind vehicle for current user
addVehicleServiceConfirmUrl: https://6l7s44jzcl.execute-api.cn-northwest-1.amazonaws.com.cn/cn-preprod/api/v1/vehicle/confirm
# Geofence Service
#
geofenceServiceEndpointUrl: https://4n0od8t9c0.execute-api.eu-west-2.amazonaws.com/v1/api/v1/geofence

#