#
# API Gateway Configurations
#
region: cn-northwest-1
deploymentStage: cn-dev
#
# Lambda VPC Configurations
vpcName: mab-cn-dev
vpcId: vpc-0710f8c908f907d84
subnetIds:
  - subnet-0da8eef37988eb7a6
  - subnet-016352bccf9a2cc1d
  - subnet-069a0dbfe4f8075db
availabilityZones:
  - cn-northwest-1a
  - cn-northwest-1b
  - cn-northwest-1c
#
# AWS Accounts
#
account: ************ # nosemgrep: generic.secrets.security.detected-aws-account-id.detected-aws-account-id
#
# VPC Link
#
#
nginxNlbArn: arn:aws-cn:elasticloadbalancing:cn-northwest-1:************:loadbalancer/net/eks-nginx-ingress-service/9da02e0e1f96f42e
apigwVpcEndpoint: execute-api
#
# ForgeRock Server
#
forgeRockHost: https://qa.identity.jaguarlandrover.cn
#
# policyArnFormat Configurations
#
policyArnFormat: arn:aws-cn:execute-api:%s:%s:%s/%s/%s/%s
#
# Approov
#
approovEnabled: false
approovSecretArn: arn:aws-cn:secretsmanager:cn-northwest-1:************:secret:oab/approov-tgaWPr
#
# Custom Domain Name
#
certificate: arn:aws-cn:acm:cn-northwest-1:************:certificate/dbcd3570-d6ce-438f-b3e4-a0992fe8b79f
customName: oneapp
domainName: mab.cn-dev.jlr-vcdp.dcclouds.com
hostedZoneId: Z03836452PTCTDENFUTQ9
privateHostedZoneId: Z06234631J44ZMMK4UYQY
#
# DDA-104438
# Create a 2nd Custom Domain for domain switch
# Presence of these will be detected in the domain function
#
certificateV2:
domainNameV2: mab.cn-dev.jlr-vcdp.dcclouds.com
hostedZoneIdV2:
privateHostedZoneIdV2:
#
# Memcached
#
cacheNodeCount: 3
#
# Command Router
#
commandRouterUrl: https://jzolpz0j0j.execute-api.cn-northwest-1.amazonaws.com.cn/cn-dev
#
# Place Service
placeServiceUrl: https://place-gateway.dev.jlr-vcdp.com
#
# Scheduling Service
#
scheduleServiceUrl: https://qi2jdk5dki.execute-api.eu-west-2.amazonaws.com/vcdp-developers/{vehicleId}/schedules
#
# Guardian Mode Service
#
guardianModeServiceScheduleEndpointUrl: https://zovodp5rp0.execute-api.eu-west-2.amazonaws.com/dev/api/v1/schedule
guardianModeServiceScheduleByIdEndpointUrl: https://zovodp5rp0.execute-api.eu-west-2.amazonaws.com/dev/api/v1/schedule/{id}
#
# Event Lambda Function Service
eventLambdaFunctionName: one-app-backend-event-lambda-function
#
# Notification Lambda Function Service
notificationLambdaFunctionName: oab-notification-registration-lambda
#
# Trip Service In Eco-Int
#
tripServiceMeEndpointUrl: https://oab-trip-service.mab.dev.jlr-vcdp.com/api/v1/users/me/vehicles/{vehicleId}/trips
tripDetailsServiceMeEndpointUrl: https://oab-trip-service.mab.dev.jlr-vcdp.com/api/v1/users/me/vehicles/{vehicleId}/trips/{tripId}
#
# Content Management Integration Service In Eco-Int
#
contentManagementServiceEndpointUrl: https://tzblpkq0dc.execute-api.cn-northwest-1.amazonaws.com.cn/cn-dev/api/v1/faqs
#
# Loqate Service In Eco-Int
#
customerApiFindAddressEndpointUrl: https://z99regwomc.execute-api.eu-west-1.amazonaws.com/dev/api/v1/addresses
customerApiRetrieveAddressEndpointUrl: https://z99regwomc.execute-api.eu-west-1.amazonaws.com/dev/api/v1/addresses/{addressId}
#
# PaaK
#
paakOwnerPairingEndpointUrl: https://xrmz85y1bc.execute-api.eu-west-2.amazonaws.com/api/v1/users/me/paak/vehicles/{vehicleId}/pairing
paakSuspendKeyEndpointUrl: https://xrmz85y1bc.execute-api.eu-west-2.amazonaws.com/api/v1/users/me/paak/vehicles/{vehicleId}/keys/{slotId}/suspend
paakResumeKeyEndpointUrl: https://xrmz85y1bc.execute-api.eu-west-2.amazonaws.com/api/v1/users/me/paak/vehicles/{vehicleId}/keys/{slotId}/resume
paakGetPhysicalKeysEndpointUrl: https://xrmz85y1bc.execute-api.eu-west-2.amazonaws.com/api/v1/users/me/paak/vehicles/{vehicleId}/keys/physical
#
# Datadog configurations
#
ddEnv: mobile-apps-backend-cn-dev
ddService: oab-api-gw
ddManagedBy: cdk
ddPiiDataHandler: false
ddProductOwner: <EMAIL>
ddRepoUrl: https://git-gdd.sdo.jlrmotor.com/D9/one-app/api-gateway/oab-api-gateway-cdk
ddSquad: pineapple
ddServiceLambdaAuthorizer: oab-api-gw-lambda-authorizer
ddWrapper: /opt/datadog_wrapper
ddSite: datadoghq.eu
ddSecretArn: arn:aws-cn:secretsmanager:cn-northwest-1:************:secret:datadog-forward-lambda-apikey20240125090018525600000001-wj6Qdj
#
# Datadog trace and extension arn version
#
ddExtension: Datadog-Extension
ddExtensionVersion: arn:aws-cn:lambda:cn-northwest-1:************:layer:Datadog-Extension:3
ddTraceJava: DD-trace-java
ddTraceJavaVersion: arn:aws-cn:lambda:cn-northwest-1:************:layer:dd-trace-java:2
lambdaInsightsArn: arn:aws:lambda:eu-west-2:580247275435:layer:LambdaInsightsExtension:45
#
# Endpoint URLs for MAB EKS services
# DDA-104438
# Will need these a 2nd time for private hosted zone at a later date
#
userServiceUsersEndpointUrl: https://xscgjz5fa9.execute-api.cn-northwest-1.amazonaws.com.cn/cn-dev/api/v1/users
userServiceUsersRegisterEndpointUrl: https://xscgjz5fa9.execute-api.cn-northwest-1.amazonaws.com.cn/cn-dev/api/v1/users/register
userServiceUsersUpdateEndpointUrl: https://xscgjz5fa9.execute-api.cn-northwest-1.amazonaws.com.cn/cn-dev/api/v1/users/update
userServiceUsersDeleteEndpointUrl: https://oab-user-service.mab.dev.jlr-vcdp.com/api/v1/users/delete
userServiceUsersAvatarEndpointUrl: https://xscgjz5fa9.execute-api.cn-northwest-1.amazonaws.com.cn/cn-dev/api/v1/users/me/avatar
userServiceUsersRetrieveConsentsEndpointUrl: https://xscgjz5fa9.execute-api.cn-northwest-1.amazonaws.com.cn/cn-dev/api/v1/users/consents
userServiceUsersUpdateConsentsEndpointUrl: https://xscgjz5fa9.execute-api.cn-northwest-1.amazonaws.com.cn/cn-dev/api/v1/users/consents
featureServiceMeEndpointUrl: https://oab-feature-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/users/{id}/features
vehicleConsentsServiceEndpointUrl: https://oab-vehicle-consents-service.mab.cn-dev.jlr-vcdp.com/api/v1/vehicles/{vehicleId}/consents
vehicleServiceMyVehiclesEndpointUrl: https://oab-vehicle-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/users/me/vehicles
vehicleServiceVehicleByIdEndpointUrl: https://oab-vehicle-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/users/me/vehicles/{vehicleId}
homeChargingServiceChargersEndpointUrl: https://oab-homecharging-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/users/me/charging/home/<USER>
homeChargingServiceChargersStartEndpointUrl: https://oab-homecharging-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/users/me/charging/home/<USER>/{chargerId}/start
homeChargingServiceChargersStopEndpointUrl: https://oab-homecharging-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/users/me/charging/home/<USER>/{chargerId}/stop
homeChargingServiceChargersStatusEndpointUrl: https://oab-homecharging-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/users/me/charging/home/<USER>/{chargerId}/charge-point-status
homeChargingServiceChargerByIdEndpointUrl: https://oab-homecharging-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/users/me/charging/home/<USER>/{chargerId}
homeChargingServiceChargerByIdDefaultScheduleEndpointUrl: https://oab-homecharging-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/users/me/charging/home/<USER>/{chargerId}/schedules/default
homeChargingServiceChargerTariffsEndpointUrl: https://oab-homecharging-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/users/me/charging/home/<USER>/{chargerId}/tariffs
homeChargingServiceChargerHistoryEndpointUrl: https://oab-homecharging-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/users/me/charging/home/<USER>/{chargerId}/history
homeChargingServiceChargerHistoryLastEndpointUrl: https://oab-homecharging-service.mab.cn-dev.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/history/last
homeChargingServiceChargerHistoryCsvEndpointUrl: https://oab-homecharging-service.mab.cn-dev.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/history/csv
publicChargingServiceUrl: https://oab-charging-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/charging/public/stations
publicChargingServiceGetFaqsEndpointUrl: https://oab-charging-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/charging/public/faqs
publicChargingServiceGetFiltersEndpointUrl: https://oab-charging-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/charging/public/filters
publicChargingServiceGetKeywordsEndpointUrl: https://oab-charging-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/charging/public/trending-keywords
publicChargingServiceGetConnectorDetailsEndpointUrl: https://oab-charging-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/charging/public/stations/{stationId}/connector-detail
publicChargingServiceChargersStartEndpointUrl: https://oab-charging-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/charging/public/command/sessions/{sessionId}/start
publicChargingServiceChargersStopEndpointUrl: https://oab-charging-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/charging/public/command/sessions/{sessionId}/stop
publicChargingServiceGetChargerDetailsEndpointUrl: https://oab-charging-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/charging/public/charge-history/sessions/charge/{sessionId}
publicChargingServiceGetOccupancyDetailsEndpointUrl: https://oab-charging-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/charging/public/charge-history/sessions/occupancy/{sessionId}
publicChargingServiceGetUnfinishedOrderEndpointUrl: https://oab-charging-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/charging/public/charge-history/sessions
publicChargingServiceGetChargingHistoryEndpointUrl: https://oab-charging-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/charging/public/charge-history/{filter}
publicChargingServiceGetChargingSessionHistoryEndpointUrl: https://oab-charging-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/charging/public/charge-history/{filter}/sessions
publicChargingServiceGetJlrContractsServiceEndpointUrl: https://oab-public-charging-service.mab.dev.jlr-vcdp.com/api/v1/vehicles/contracts
preferenceServiceMyPreferencesEndpointUrl: https://oab-preference-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/users/me/preferences
deviceVerificationServiceEndpointUrl: https://oab-device-verification-service.mab.dev.jlr-vcdp.com/api/v1/verifications
feedbackEndpointUrl: https://ys06lf5wm4.execute-api.cn-northwest-1.amazonaws.com.cn/cn-dev/api/v1/feedback
formEndpointUrl: https://ys06lf5wm4.execute-api.cn-northwest-1.amazonaws.com.cn/cn-dev/api/v1/form/{formId}/{language}
retailerServiceEndpointUrl: https://0l7rqy7jl6.execute-api.cn-northwest-1.amazonaws.com.cn/cn-dev/api/v1/retailers
retailerApiServiceTypesEndpointUrl: https://0l7rqy7jl6.execute-api.cn-northwest-1.amazonaws.com.cn/cn-dev/api/v1/retailers/service-types
retailerApiCitiesEndpointUrl: https://0l7rqy7jl6.execute-api.cn-northwest-1.amazonaws.com.cn/cn-dev/api/v1/retailers/cities
retailerApiFavoriteEndpointUrl: https://0l7rqy7jl6.execute-api.cn-northwest-1.amazonaws.com.cn/cn-dev/api/v1/retailers/{tradingCode}/favorite
notificationIntegrationServiceUrl: https://m1w4fjmd7c.execute-api.cn-northwest-1.amazonaws.com.cn/cn-dev/api/v1/users/me/devices

#
# Vehicle Position Service In Eco-Int
#
vehiclePositionServiceMeEndpointUrl: https://uy33ews0xh.execute-api.eu-west-1.amazonaws.com/dev/api/v1/users/me/vehicles/{vehicleId}/positions/latest
vehicleParkedPositionServiceMeEndpointUrl: https://uy33ews0xh.execute-api.eu-west-1.amazonaws.com/dev/api/v1/users/me/vehicles/{vehicleId}/positions/parked
#
# Service Booking Services In Eco-Int
#
serviceBookingSubmitUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings
serviceBookingDetailsUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings/{bookingNo}
serviceBookingCancelUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings/{bookingNo}
serviceBookingListUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings/query
serviceBookingFileUploadUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings/upload
serviceBookingLastDealerUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/dealers/last
serviceBookingServiceAdvisorsUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/dealers/{dealerNo}/advisors
serviceBookingDealerScheduleUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/dealers/{dealerNo}/schedules
#
# [CN]Add Vehicle Service In Eco-Int
#
# upload IDCard/VehicleLicense/VehicleInvoice for current user
addVehicleServiceRecognizeUrl: https://vzz8jrtbdb.execute-api.cn-northwest-1.amazonaws.com.cn/cn-dev/api/v1/vehicle/recognize
# bind vehicle for current user
addVehicleServiceConfirmUrl: https://vzz8jrtbdb.execute-api.cn-northwest-1.amazonaws.com.cn/cn-dev/api/v1/vehicle/confirm
# Geofence Service
#
geofenceServiceEndpointUrl: https://sslbsy59a0.execute-api.eu-west-2.amazonaws.com/v1/api/v1/geofence
#