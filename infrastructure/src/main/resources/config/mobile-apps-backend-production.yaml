#
# API Gateway Configurations
#
region: eu-west-2
deploymentStage: production
#
# Lambda VPC Configurations
#
vpcName: jlr-gdd-mab-prod-vpc
vpcId: vpc-0ce15869842f6d1c9
subnetIds:
  - subnet-09accbd13153bdcf5
  - subnet-070d1e9cbd9465e61
  - subnet-08604bf57e348cdc4
availabilityZones:
  - eu-west-2a
  - eu-west-2b
  - eu-west-2c
#
# AWS Accounts
#
account: ************ # nosemgrep: generic.secrets.security.detected-aws-account-id.detected-aws-account-id
#
# VPC Link
#
nginxNlbArn: arn:aws:elasticloadbalancing:eu-west-2:************:loadbalancer/net/eks-nginx-ingress-service/eba668bcbcaa2c3b
apigwVpcEndpoint: execute-api
#
# ForgeRock Server
#
forgeRockHost: https://identity.jaguarlandrover.com
#
# policyArnFormat Configurations
#
policyArnFormat: arn:aws:execute-api:%s:%s:%s/%s/%s/%s
#
# Approov
#
approovEnabled: true
approovSecretArn: arn:aws:secretsmanager:eu-west-2:************:secret:oab/approov-E5JN45
#
# Custom Domain Name
#
certificate: arn:aws:acm:eu-west-2:************:certificate/c80f3552-bf58-4892-a1ef-4446e92f20f7
customName: oneapp
domainName: mab.prod.jlr-vcdp.com
hostedZoneId: Z021885019NTNC7PMZ83J
privateHostedZoneId: Z08055751UH5ZFFHIWCCD
#
# DDA-104438
# Create a 2nd Custom Domain for domain switch
# Presence of these will be detected in the domain function
#
certificateV2:
domainNameV2: mab-prod.jlr-vcdp.com
hostedZoneIdV2:
privateHostedZoneIdV2:
#
# Memcached
#
cacheNodeCount: 3
#
# Command Router
#
commandRouterUrl: https://t88r348c19.execute-api.eu-west-2.amazonaws.com/prod
#
# Place Service
placeServiceUrl: https://place-gateway.prod.jlr-vcdp.com
#
# Scheduling Service
#
scheduleServiceUrl: https://u1rc0wi2y3.execute-api.eu-west-2.amazonaws.com/vcdp-production/{vehicleId}/schedules
#
# Guardian Mode Service
#
guardianModeServiceScheduleEndpointUrl: https://uijm2uoorf.execute-api.eu-west-2.amazonaws.com/prod/api/v1/schedule
guardianModeServiceScheduleByIdEndpointUrl: https://uijm2uoorf.execute-api.eu-west-2.amazonaws.com/prod/api/v1/schedule/{id}
#
# Event Lambda Function Service
eventLambdaFunctionName: one-app-backend-event-lambda-function
#
# Notification Lambda Function Service
notificationLambdaFunctionName: oab-notification-registration-lambda
#
# Trip Service
#
tripServiceMeEndpointUrl: https://qzl6mqoqr3.execute-api.eu-west-1.amazonaws.com/production/api/v1/users/me/vehicles/{vehicleId}/trips
tripDetailsServiceMeEndpointUrl: https://qzl6mqoqr3.execute-api.eu-west-1.amazonaws.com/production/api/v1/users/me/vehicles/{vehicleId}/trips/{tripId}
#
# Content Management Integration Service In Eco-Int
#
contentManagementServiceEndpointUrl: https://qm3vtcci61.execute-api.eu-west-1.amazonaws.com/dev/api/v1/faqs
#
# Loqate Service In Eco-Int
#
customerApiFindAddressEndpointUrl: https://agzw89h78e.execute-api.eu-west-1.amazonaws.com/production/api/v1/addresses
customerApiRetrieveAddressEndpointUrl: https://agzw89h78e.execute-api.eu-west-1.amazonaws.com/production/api/v1/addresses/{addressId}
#
# Datadog configurations
#
ddEnv: mobile-apps-backend-production
ddService: oab-api-gw
ddManagedBy: cdk
ddPiiDataHandler: false
ddProductOwner: <EMAIL>
ddRepoUrl: https://git-gdd.sdo.jlrmotor.com/D9/one-app/api-gateway/oab-api-gateway-cdk
ddSquad: pineapple
ddServiceLambdaAuthorizer: oab-api-gw-lambda-authorizer
ddWrapper: /opt/datadog_wrapper
ddSite: datadoghq.eu
ddSecretArn: arn:aws:secretsmanager:eu-west-2:************:secret:DdApiKeySecret-NLxU7ie3ewbQ-N5PHA5
#
# Datadog trace and extension arn version
#
ddExtension: Datadog-Extension
ddExtensionVersion: arn:aws:lambda:eu-west-2:464622532012:layer:Datadog-Extension:55
ddTraceJava: DD-trace-java
ddTraceJavaVersion: arn:aws:lambda:eu-west-2:464622532012:layer:dd-trace-java:14
lambdaInsightsArn: arn:aws:lambda:eu-west-2:580247275435:layer:LambdaInsightsExtension:45
#
# Endpoint URLs for EKS services
#
userServiceUsersEndpointUrl: https://oab-user-service.mab.prod.jlr-vcdp.com/api/v1/users
userServiceUsersRegisterEndpointUrl: https://oab-user-service.mab.prod.jlr-vcdp.com/api/v1/users/register
userServiceUsersUpdateEndpointUrl: https://oab-user-service.mab.prod.jlr-vcdp.com/api/v1/users/update
userServiceUsersDeleteEndpointUrl: https://oab-user-service.mab.prod.jlr-vcdp.com/api/v1/users/delete
userServiceUsersRetrieveConsentsEndpointUrl: https://oab-user-service.mab.prod.jlr-vcdp.com/api/v1/users/consents
userServiceUsersUpdateConsentsEndpointUrl: https://oab-user-service.mab.prod.jlr-vcdp.com/api/v1/users/consents
featureServiceMeEndpointUrl: https://oab-feature-service.mab.prod.jlr-vcdp.com/api/v1/users/{id}/features
vehicleConsentsServiceEndpointUrl: https://oab-vehicle-consents-service.mab.prod.jlr-vcdp.com/api/v1/vehicles/{vehicleId}/consents
vehicleServiceMyVehiclesEndpointUrl: https://oab-vehicle-service.mab.prod.jlr-vcdp.com/api/v1/users/me/vehicles
vehicleServiceVehicleByIdEndpointUrl: https://oab-vehicle-service.mab.prod.jlr-vcdp.com/api/v1/users/me/vehicles/{vehicleId}
homeChargingServiceChargersEndpointUrl: https://oab-homecharging-service.mab.prod.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>
homeChargingServiceChargersStartEndpointUrl: https://oab-homecharging-service.mab.prod.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/start
homeChargingServiceChargersStopEndpointUrl: https://oab-homecharging-service.mab.prod.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/stop
homeChargingServiceChargersStatusEndpointUrl: https://oab-homecharging-service.mab.prod.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/charge-point-status
homeChargingServiceChargerByIdEndpointUrl: https://oab-homecharging-service.mab.prod.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}
homeChargingServiceChargerByIdDefaultScheduleEndpointUrl: https://oab-homecharging-service.mab.prod.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/schedules/default
homeChargingServiceChargerTariffsEndpointUrl: https://oab-homecharging-service.mab.prod.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/tariffs
homeChargingServiceChargerHistoryEndpointUrl: https://oab-homecharging-service.mab.prod.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/history
homeChargingServiceChargerHistoryLastEndpointUrl: https://oab-homecharging-service.mab.prod.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/history/last
homeChargingServiceChargerHistoryCsvEndpointUrl: https://oab-homecharging-service.mab.prod.jlr-vcdp.com/api/v1/users/me/charging/home/<USER>/{chargerId}/history/csv
publicChargingServiceUrl: https://oab-public-charging-service.mab.prod.jlr-vcdp.com/api/v1/charging/public/stations
publicChargingServiceGetFaqsEndpointUrl: https://oab-public-charging-service.mab.prod.jlr-vcdp.com/api/v1/charging/public/faqs
publicChargingServiceGetFiltersEndpointUrl: https://oab-public-charging-service.mab.prod.jlr-vcdp.com/api/v1/charging/public/filters
publicChargingServiceGetKeywordsEndpointUrl: https://oab-public-charging-service.mab.prod.jlr-vcdp.com/api/v1/charging/public/trending-keywords
publicChargingServiceGetConnectorDetailsEndpointUrl: https://oab-public-charging-service.mab.prod.jlr-vcdp.com/api/v1/charging/public/stations/{stationId}/connector-detail
publicChargingServiceChargersStartEndpointUrl: https://oab-public-charging-service.mab.prod.jlr-vcdp.com/api/v1/charging/public/command/sessions/{sessionId}/start
publicChargingServiceChargersStopEndpointUrl: https://oab-public-charging-service.mab.prod.jlr-vcdp.com/api/v1/charging/public/command/sessions/{sessionId}/stop
publicChargingServiceGetChargerDetailsEndpointUrl: https://oab-public-charging-service.mab.prod.jlr-vcdp.com/api/v1/charging/public/charge-history/sessions/charge/{sessionId}
publicChargingServiceGetOccupancyDetailsEndpointUrl: https://oab-public-charging-service.mab.prod.jlr-vcdp.com/api/v1/charging/public/charge-history/sessions/occupancy/{sessionId}
publicChargingServiceGetUnfinishedOrderEndpointUrl: https://oab-public-charging-service.mab.prod.jlr-vcdp.com/api/v1/charging/public/charge-history/sessions
publicChargingServiceGetChargingHistoryEndpointUrl: https://oab-public-charging-service.mab.prod.jlr-vcdp.com/api/v1/charging/public/charge-history/{filter}
publicChargingServiceGetChargingSessionHistoryEndpointUrl: https://oab-public-charging-service.mab.prod.jlr-vcdp.com/api/v1/charging/public/charge-history/{filter}/sessions
publicChargingServiceGetJlrContractsServiceEndpointUrl: https://oab-public-charging-service.mab.prod.jlr-vcdp.com/api/v1/users/me/contracts
preferenceServiceMyPreferencesEndpointUrl: https://oab-preference-service.mab.prod.jlr-vcdp.com/api/v1/users/me/preferences
deviceVerificationServiceEndpointUrl: https://oab-device-verification-service.mab.prod.jlr-vcdp.com/api/v1/verifications
#
# Vehicle Position Service In Eco-Int
#
vehiclePositionServiceMeEndpointUrl: https://qzl6mqoqr3.execute-api.eu-west-1.amazonaws.com/production/api/v1/users/me/vehicles/{vehicleId}/positions/latest
vehicleParkedPositionServiceMeEndpointUrl: https://qzl6mqoqr3.execute-api.eu-west-1.amazonaws.com/production/api/v1/users/me/vehicles/{vehicleId}/positions/parked
#
# Service Booking Services In Eco-Int
#
serviceBookingSubmitUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings
serviceBookingDetailsUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings/{bookingNo}
serviceBookingCancelUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings/{bookingNo}
serviceBookingListUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings/query
serviceBookingFileUploadUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/bookings/upload
serviceBookingLastDealerUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/dealers/last
serviceBookingServiceAdvisorsUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/dealers/{dealerNo}/advisors
serviceBookingDealerScheduleUrl: https://oab-service-booking-bff-service.mab.cn-dev.jlr-vcdp.dcclouds.com/api/v1/service-booking/dealers/{dealerNo}/schedules
#
# Geofence Service
#
geofenceServiceEndpointUrl: https://geofence-config.production.jlr-vcdp.com/api/v1/geofence
#