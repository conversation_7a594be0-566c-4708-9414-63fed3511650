<?xml version="1.0" encoding="UTF-8"?>

<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jaguarlandrover.d9.oneappbackend</groupId>
    <artifactId>oab-api-gateway-infrastructure</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>CDK infrastructure codes for OneApp Backend API Gateway</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>

        <!-- Versions for Dependencies -->
        <cdk.version>2.126.0</cdk.version>
        <constructs.version>[10.0.0,11.0.0)</constructs.version>
        <jackson.version>2.15.0-rc1</jackson.version>
        <junit.version>5.10.2</junit.version>
        <junit-pioneer.version>2.2.0</junit-pioneer.version>
        <log4j.version>2.23.1</log4j.version>
        <lombok.version>1.18.32</lombok.version>
        <spock.version>2.4-M4-groovy-4.0</spock.version>

        <!-- Versions for Plugins -->
        <exec-maven-plugin.version>3.1.0</exec-maven-plugin.version>
        <gmavenplus-plugin.version>3.0.2</gmavenplus-plugin.version>
        <jacoco-maven-plugin.version>0.8.12</jacoco-maven-plugin.version>
        <maven-checkstyle-plugin.version>3.4.0</maven-checkstyle-plugin.version>
        <maven-surefire-plugin.version>3.3.0</maven-surefire-plugin.version>
    </properties>

    <dependencies>
        <!-- AWS Cloud Development Kit -->
        <dependency>
            <groupId>software.amazon.awscdk</groupId>
            <artifactId>aws-cdk-lib</artifactId>
            <version>${cdk.version}</version>
        </dependency>

        <dependency>
            <groupId>software.constructs</groupId>
            <artifactId>constructs</artifactId>
            <version>${constructs.version}</version>
        </dependency>

        <!-- Required by Lambda Function logging -->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
            <version>${log4j.version}</version>
        </dependency>

        <!-- Others -->
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-yaml</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20231013</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>

        <!-- Testing -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit-pioneer</groupId>
            <artifactId>junit-pioneer</artifactId>
            <version>${junit-pioneer.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <version>${spock.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- detect tests -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <includes>
                        <include>**/*Spec</include>
                        <include>**/*Test</include>
                    </includes>
                    <failIfNoTests>true</failIfNoTests>
                    <useFile>false</useFile>
                    <!--
                        Make it able to modify environment variables for tests.
                        Ref:  https://junit-pioneer.org/docs/environment-variables/#warnings-for-reflective-access
                    -->
                    <argLine>
                        --add-opens java.base/java.util=ALL-UNNAMED
                        --add-opens java.base/java.lang=ALL-UNNAMED
                    </argLine>
                </configuration>
            </plugin>

            <!-- Spock tests -->
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>${gmavenplus-plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>addTestSources</goal>
                            <goal>compile</goal>
                            <goal>compileTests</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Used by `cdk synth` command -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>${exec-maven-plugin.version}</version>
                <configuration>
                    <mainClass>com.jaguarlandrover.d9.oneappbackend.OabApiGatewayApp</mainClass>
                </configuration>
            </plugin>

            <!-- Check source code formatting -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>${maven-checkstyle-plugin.version}</version>
                <configuration>
                    <configLocation>../config/checkstyle.xml</configLocation>
                    <consoleOutput>true</consoleOutput>
                    <violationSeverity>warning</violationSeverity>
                    <failsOnError>true</failsOnError>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Compute and report code coverage -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
